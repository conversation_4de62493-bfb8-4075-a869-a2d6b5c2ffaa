module.exports = function (app, server, Player, Table) {
    delete require.cache[require.resolve('./Require')];
    var gameInstance = require('./Require')(app);

    Table.prototype.initTable = function () {
        this.tData = {
            tableid: this.tableid,
            jipaiqi: [],                //使用了记牌器的人
            initCoin: 0,
            gameType: 0,				//房间类型(哪个地区的)
            roundAll: 0,				//总局数
            roundNum: -1,               //剩余局数
            extraNum: 0,                //加时局数
            extraTimeType: 0,           // 加时赛的方式 0: 无加时 1:红包加时赛 2:普通加时
            extraTimeVote: {},          // 投票信息
            matchScoreLimitUser: {},      // 比赛场分数受限制的玩家
            winOneData: {},               // 每一小局结束的分数 {uid1:[], uid2:[], uid:3}
            isValidTable: false,       //至少打了一局的房间
            fanNum: 0,				    //番数
            maxPlayer: 4,		  	    //最大人数
            uids: [],                   //uid列表
            uidsQueue: [],              //uid进房间顺序
            owner: -1,         		    //创建房间者的uid
            maxHunNum: 4,			    //最大混子数量
            cardNext: 0,				//牌的索引index
            winner: -1,        		    //赢家 0-3
            zhuang: -1,        		    //庄 0-3
            zhuangBeishu: 0,            //庄家倍数 0-4
            curPlayer: -1,     		    //当前发到牌的玩家或者当前抢杠的玩家 0-3
            lastPutPlayer: -1, 		    //上次出牌玩家 0-3
            putType: 0,        		    //出牌类型 杠，还是吃，还是其他
            lastDrawPlayer: -1,         //上次摸牌玩家 (字牌)
            tState: TableState.waitJoin,//房间当前状态
            lastPutCard: -1,       	    //上次出的牌
            genpaiCount: 0,			    //开局跟牌数，-1停止跟牌，等于3完成跟牌
            isHunCardPlay: false,	    //客户端是否选择有癞子的玩法
            canHu7: false,			    //客户端能否胡七对
            freeBegin: 0,               //自由房间倒计时时间
            firstFree: 0,               //自由房间提出投票的人
            delEnd: 0,                  //解散房间的计时时间
            delEndHePai: 0,             //和牌的计时时间
            firstDel: 0,       		    //提出解散房间的人的uid
            canEatHu: true,
            huangNum: 14,			    //黄庄数
            lastGangType: 0,            //明杠暗杠标志
            lastGangDianpaoPlayer: -1,  //记录杠上花点炮算作点炮的玩家
            xingPlayer: -1,             //跑胡子4人玩法中的醒家
            datuoNum: 0,                //打坨人数
            shuffleCardsNum: 0,         //洗牌的数量
            hunCard: 0,                 //明牌（花牌）
            shuffler: -1,               //切牌玩家idx
            mustJiao: false,           //是否必分
            zhuafenlist: [],            //已打出的分牌
            roundzhuafen: 0,            //当轮打出的分
            allPutCards: [],            // 该局所有玩家打出的牌
            doubleCard: [],              //使用了超级加倍卡的人
            badUIds: [],
            luckRang: '',
            rungingUids: [],            //正在游戏的用户id
            watchingUids: [],           //旁观用户id
        };
        var tData = this.tData;
        tData.owner = this.createParams.owner;
        tData.roundAll = this.createParams.round;
        tData.roundNum = this.createParams.round;
        tData.gameType = this.createParams.gameType;
        tData.maxPlayer = this.createParams.maxPlayer;
        tData.areaSelectMode = {
            maxPlayer: this.createParams.maxPlayer,
            //防沉迷
            isAntiAddictionDissolveLimit: this.createParams.isAntiAddictionDissolveLimit,
            antiAddictionDissolveLimitScore: this.createParams.antiAddictionDissolveLimitScore,
            antiAddictionLimitScore: this.createParams.antiAddictionLimitScore,
            isAntiAddictionLimit: this.createParams.isAntiAddictionLimit,

            //测试数据，需删除
            //最低抽成分数
            commissionBottomScore: this.createParams.commissionBottomScore,
            //是否固定抽成
            isFixRate: this.createParams.isFixRate,
            //抽成比例
            commissionRate: this.createParams.commissionRate,
            //洗牌分数
            xiPaiLimitScore: this.createParams.xiPaiLimitScore || 1,
        };

        logger.debug("创房参数", JSON.stringify(this.createParams));
        //防沉迷
        // if (this.createParams.isAntiAddictionDissolveLimit == 1 && this.createParams.antiAddictionScoreNeedEnough == 1) { // 开启比赛自动解散 && 开启分数不为负
        //     tData.areaSelectMode.antiAddictionScoreNeedEnough = 1; // 前端计算分数不为负
        // } else {
        //     tData.areaSelectMode.antiAddictionScoreNeedEnough = 0;
        // }
        this.createParams.isAntiAddictionDissolveLimit == 1
        tData.areaSelectMode.antiAddictionScoreNeedEnough = 1;

        if (GAME_CFG.defaultSelected && GAME_CFG.defaultSelected[tData.gameType]) {
            for (var key in GAME_CFG.defaultSelected[tData.gameType]) {
                if (!(key in this.createParams)) {
                    this.createParams[key] = GAME_CFG.defaultSelected[tData.gameType][key];
                }
            }
        }
        if (this.createParams.clubId) {
            tData.clubId = this.createParams.clubId;
            tData.ruleId = this.createParams.ruleId;
            tData.ruleName = this.createParams.ruleName;
            tData.clubAvatar = this.createParams.clubAvatar;
            tData.clubTitle = this.createParams.clubTitle;
            tData.clubType = -1;
        }

        if (this.createParams.gps) {
            tData.areaSelectMode['gps'] = this.createParams.gps;
        }
        if (utils.isStrNotEmpty(this.createParams.payWay)) {
            tData.areaSelectMode['payWay'] = this.createParams.payWay;
        }
        if (utils.isStrNotEmpty(this.createParams.isQuan)) {
            tData.areaSelectMode['isQuan'] = this.createParams.isQuan;
        }
        if (utils.isStrNotEmpty(this.createParams.trustTime)) {
            tData.areaSelectMode['trustTime'] = this.createParams.trustTime;// 托管时间
        }
        if (utils.isStrNotEmpty(this.createParams.trustWay)) {
            tData.areaSelectMode['trustWay'] = this.createParams.trustWay;// 托管方式
        }
        if (utils.isStrNotEmpty(this.createParams.isTrustWhole)) {
            tData.areaSelectMode["isTrustWhole"] = this.createParams.isTrustWhole;// 全局托管
        }
        if (utils.isStrNotEmpty(this.createParams.convertible)) {
            tData.areaSelectMode['convertible'] = this.createParams.convertible;
        }

        if (utils.isStrNotEmpty(this.createParams.realtimeVoice)) {
            tData.realtimeVoice = this.createParams.realtimeVoice;
            tData.areaSelectMode['realtimeVoice'] = this.createParams.realtimeVoice;
        }
        if ('isOpenTingTip' in this.createParams) {
            tData.areaSelectMode['isOpenTingTip'] = this.createParams.isOpenTingTip;
        }
        if (tData.gameType == GAME_TYPE.LIAN_YUN_GANG) {
            this.pGameCode = gameInstance.pGameCodeLianYunGang;
        }
        else if (tData.gameType == GAME_TYPE.SHU_YANG) {
            this.pGameCode = gameInstance.pGameCodeShuyang;
        }
        else if (tData.gameType == GAME_TYPE.GUAN_NAN) {
            logger.debug("=====GameCode=====runStartGame===========灌南=====================");
            this.pGameCode = gameInstance.pGameCodeGuannan;
            tData.areaSelectMode["payType"] = this.createParams.payType;  //点炮时候 包一还是包三
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.areaSelectMode["daJue7DuiXianHu"] = !this.createParams.duoHu && this.createParams.daJue7DuiXianHu;  // 大绝7对先胡
            tData.areaSelectMode["zhuang2"] = this.createParams.zhuang2;  // 0:庄闲同分   1:庄2闲1千胡翻倍   2:庄2闲1千胡不翻倍
            tData.gameCnName = "灌南";
        }
        else if (tData.gameType == GAME_TYPE.GUAN_YUN) {
            logger.debug("=====GameCode=====runStartGame===========灌云=====================");
            this.pGameCode = gameInstance.pGameCodeGuanyun;
            tData.areaSelectMode["flowerType"] = this.createParams.flowerType;//花牌
            tData.areaSelectMode["payType"] = this.createParams.payType;//点炮时候 包一还是包三
            tData.areaSelectMode["qiduiketianhu"] = this.createParams.qiduiketianhu;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.gameCnName = "灌云";
        }
        else if (tData.gameType == GAME_TYPE.DONG_HAI) {
            logger.debug("=====GameCode=====runStartGame===========东海=====================");
            this.pGameCode = gameInstance.pGameCodeDonghai;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.areaSelectMode["isJiaZhu"] = this.createParams.isJiaZhu;
            tData.gameCnName = "东海";
        }
        else if (tData.gameType == GAME_TYPE.NAN_JING) {
            logger.debug("=====GameCode=====runStartGame===========南京=====================");
            this.pGameCode = gameInstance.pGameCodeNanjing;
            tData.areaSelectMode["playType"] = this.createParams.playType;
            tData.areaSelectMode["isHuaZa"] = this.createParams.isHuaZa;
            tData.areaSelectMode["isJieZhuangBi"] = this.createParams.isJieZhuangBi;
            tData.areaSelectMode["islimit300"] = this.createParams.islimit300;
        }
        else if (tData.gameType == GAME_TYPE.SU_QIAN) {
            logger.debug("=====GameCode=====runStartGame===========宿迁=====================");
            this.pGameCode = gameInstance.pGameCodeSuqian;
            tData.areaSelectMode["lazhuangFen"] = this.createParams.lazhuangFen;
            tData.areaSelectMode["tingHuType"] = this.createParams.tingHuType;
            tData.gameCnName = "宿迁";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_AN) {
            logger.debug("=====GameCode=====runStartGame===========淮安=====================");
            this.pGameCode = gameInstance.pGameCodeHuaian;
            tData.areaSelectMode["flowerType"] = this.createParams.flowerType;//花牌
            tData.areaSelectMode["zengfen"] = this.createParams.zengfen;//增分 bool
            tData.areaSelectMode["upLimmit"] = this.createParams.upLimmit;//上限 int
            tData.areaSelectMode["canGangPeng"] = this.createParams.canGangPeng;//是否可明杠可碰 bool
            tData.areaSelectMode["zhuangDouble"] = this.createParams.zhuangDouble;//庄翻倍
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "淮安";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_AN_TTZ) {
            logger.debug("=====GameCode=====runStartGame===========淮安团团转=====================");
            this.pGameCode = gameInstance.pGameCodeHuaianTTZ;
            tData.areaSelectMode["flowerType"] = this.createParams.flowerType;//花牌
            tData.areaSelectMode["tianHu"] = this.createParams.tianHu;//是否有天胡 bool
            tData.areaSelectMode["diHu"] = this.createParams.diHu;//是否有地胡 bool
            tData.areaSelectMode["mustzimo"] = this.createParams.mustzimo;// 只能自摸
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.gameCnName = "淮安团团转";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_AN_ERZ) {
            logger.debug("=====GameCode=====runStartGame===========淮安二人转=====================");
            this.pGameCode = gameInstance.pGameCodeHuaianERZ;
            tData.areaSelectMode["huaianERZ_flowerNum"] = this.createParams.huaianERZ_flowerNum;//花牌
            tData.areaSelectMode["qiduiKeBuTing"] = this.createParams.qiduiKeBuTing;// 七对可不架听
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.gameCnName = "淮安二人转";
        }
        else if (tData.gameType == GAME_TYPE.HONG_ZE_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========洪泽麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHongZeMJ;
            tData.areaSelectMode["canRob"] = this.createParams.canRob;
            tData.areaSelectMode["mbzdqz"] = this.createParams.mbzdqz;
            tData.areaSelectMode["isQiDui"] = this.createParams.isQiDui;
            tData.areaSelectMode["buDaiHongZhong"] = this.createParams.buDaiHongZhong;
            tData.areaSelectMode["zhuaMaNum"] = this.createParams.zhuaMaNum;
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "洪泽麻将";
        }
        else if (tData.gameType == GAME_TYPE.HA_HONGZHONG) {
            logger.debug("=====GameCode=====runStartGame===========淮安红中=====================");
            this.pGameCode = gameInstance.pGameCodeHAHZ;
            tData.areaSelectMode["zaimo"] = this.createParams.zaimo;
            tData.areaSelectMode["canRob"] = this.createParams.canRob;
            tData.areaSelectMode["mbzdqz"] = this.createParams.mbzdqz;
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "淮安红中";
        }
        else if (tData.gameType == GAME_TYPE.XIN_PU_HZ) {
            logger.debug("=====GameCode=====runStartGame===========红中麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHZMJ;
            tData.areaSelectMode["zhuaMaNum"] = this.createParams.zhuaMaNum || 0;          // 不抓码0、2张、4张、6张
            tData.areaSelectMode["daihongzhong"] = this.createParams.daihongzhong;    // 带红中
            tData.areaSelectMode["qiangganghu"] = this.createParams.qiangganghu;      // 抢杠胡
            tData.areaSelectMode["duoHu"] = true;   // 一包多响
            tData.gameCnName = "红中麻将";
        }
        else if (tData.gameType == GAME_TYPE.HA_14DUN) {
            logger.debug("=====GameCode=====runStartGame===========淮安14墩=====================");
            this.pGameCode = gameInstance.pGameCodeHA14D;
            tData.areaSelectMode["zhongfabai"] = this.createParams.zhongfabai;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.gameCnName = "淮安14墩";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_AN_CC) {
            logger.debug("=====GameCode=====runStartGame===========淮安出铳=====================");
            this.pGameCode = gameInstance.pGameCodeHuaianCC;
            // zhongfabai = 1:【红中当小花】红中当花, 牌局中除去发,白
            // zhongfabai = 2:【中发白白当小花】 中发白当花
            // zhongfabai = 3:【中发白不当花】 中发白作为普通牌
            tData.areaSelectMode["zhongfabai"] = this.createParams.zhongfabai;
            tData.areaSelectMode["canGangScore"] = this.createParams.canGangScore;// bool //杠牌有分
            tData.areaSelectMode["getGangScore"] = this.createParams.getGangScore;// bool //杠牌直接得分
            tData.areaSelectMode["liu8zhang"] = this.createParams.liu8zhang;// bool //是否留8张
            tData.areaSelectMode["lazhuang"] = this.createParams.lazhuang;// bool //是否拉庄
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.areaSelectMode["keZiSuan1Hua"] = this.createParams.keZiSuan1Hua;// 刻子算一花
            tData.gameCnName = "淮安出铳";
        }
        else if (tData.gameType == GAME_TYPE.HZ_TUI_DAO_HU) {
            logger.debug("=====GameCode=====runStartGame===========洪泽推倒胡=====================");
            this.pGameCode = gameInstance.pGameCodeHZTuiDaoHu;
            tData.areaSelectMode["zhongfabai"] = this.createParams.zhongfabai;
            // zhongfabai = 1:【红中当小花】红中当花, 牌局中除去发,白
            // zhongfabai = 2:【中发白白当小花】 中发白当花
            // zhongfabai = 3:【中发白不当花】 中发白作为普通牌
            tData.areaSelectMode["canGangScore"] = this.createParams.canGangScore;// bool //杠牌有分
            tData.areaSelectMode["getGangScore"] = this.createParams.getGangScore;// bool //杠牌直接得分
            tData.areaSelectMode["liu8zhang"] = this.createParams.liu8zhang;// bool //是否留8张
            tData.areaSelectMode["lazhuang"] = this.createParams.lazhuang;// bool //是否拉庄
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.gameCnName = "洪泽推到胡";
        }
        else if (tData.gameType == GAME_TYPE.XU_ZHOU) {
            logger.debug("=====GameCode=====runStartGame===========徐州=====================");
            this.pGameCode = gameInstance.pGameCodeXuzhou;
            tData.areaSelectMode["isJiaZhu"] = this.createParams.isJiaZhu;
            tData.areaSelectMode["isJiaZhuyizui"] = this.createParams.isJiaZhuyizui;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.gameCnName = "徐州";
        }
        else if (tData.gameType == GAME_TYPE.TUAN_TUAN_ZHUAN) {
            logger.debug("=====GameCode=====runStartGame===========团团转=====================");
            this.pGameCode = gameInstance.pGameCodeTuantuanzhuan;
            tData.gameCnName = "掼蛋";
        }
        else if (tData.gameType == GAME_TYPE.SI_YANG) {
            logger.debug("=====GameCode=====runStartGame===========泗阳=====================");
            this.pGameCode = gameInstance.pGameCodeSiyang;
            tData.areaSelectMode["duoHu"] = true;
            tData.areaSelectMode["zuizi"] = !!this.createParams.zuizi ? Math.max(2, this.createParams.zuizi) : 0; // 把老版本的true改为2
            tData.areaSelectMode["mustTing"] = this.createParams.mustTing;
            tData.areaSelectMode["difen"] = this.createParams.difen;
            tData.areaSelectMode["quickly"] = this.createParams.quickly;
            tData.areaSelectMode["qiaogang"] = this.createParams.qiaogang;
            tData.gameCnName = "泗阳";
        }
        else if (tData.gameType == GAME_TYPE.XIN_SI_YANG) {
            logger.debug("=====GameCode=====runStartGame===========新泗阳=====================");
            this.pGameCode = gameInstance.pGameCodeSiyangXin;
            tData.areaSelectMode["duoHu"] = true;
            tData.areaSelectMode["zuizi"] = 0;
            tData.areaSelectMode["mustTing"] = false;
            // tData.areaSelectMode["qiaogang"] = this.createParams.qiaogang;
            // tData.areaSelectMode["quickly"] = this.createParams.quickly;
            // tData.areaSelectMode["difen"] = this.createParams.difen;
            tData.gameCnName = "新泗阳";
        }
        else if (tData.gameType == GAME_TYPE.SI_YANG_HH) {
            logger.debug("=====GameCode=====runStartGame===========泗阳晃晃=====================");
            this.pGameCode = gameInstance.pGameCodeSiyangHH;
            tData.areaSelectMode["baseScore"] = this.createParams.baseScore;
            tData.areaSelectMode["qiangganghu"] = this.createParams.qiangganghu;
            tData.gameCnName = "泗阳晃晃";
        }
        else if (tData.gameType == GAME_TYPE.PAO_HU_ZI || tData.gameType == GAME_TYPE.PAO_HU_ZI_SR || tData.gameType == GAME_TYPE.PAO_HU_ZI_LR) {

            if (utils.isContains(['shaoyang', 'shaoyang-test', 'dev-a', 'dev-b'], env)) {
                logger.debug("=====GameCode=====runStartGame===========永州跑胡子(邵阳)=====================");
                this.pGameCode = gameInstance.pGameCodeSYCHZ;

            } else {
                logger.debug("=====GameCode=====runStartGame===========永州跑胡子=====================");
                this.pGameCode = gameInstance.pGameCodeYZCHZ;
            }
        }
        else if (tData.gameType == GAME_TYPE.PAO_HU_ZI_King || tData.gameType == GAME_TYPE.PAO_HU_ZI_SR_King || tData.gameType == GAME_TYPE.PAO_HU_ZI_LR_King) {
            if (utils.isContains(['shaoyang', 'shaoyang-test', 'dev-a', 'dev-b'], env)) {
                logger.debug("=====GameCode=====runStartGame===========永州跑胡子4王玩法(邵阳)=====================");
                this.pGameCode = gameInstance.pGameCodeSYCHZ_King;
            } else {
                logger.debug("=====GameCode=====runStartGame===========永州跑胡子4王玩法=====================");
                this.pGameCode = gameInstance.pGameCodeYZCHZ_King;
                tData.areaSelectMode["isKing"] = this.createParams.isKing;
                if (tData.areaSelectMode.isKing === true) {
                    tData.areaSelectMode.isKing = 0;
                } else if (tData.areaSelectMode.isKing === false) {
                    tData.areaSelectMode.isKing = 1;
                }
                tData.areaSelectMode["qihuTun"] = "qihuTun" in this.createParams ? this.createParams.qihuTun : 2;
                tData.areaSelectMode["isFanXing"] = this.createParams.isFanXing;
                tData.areaSelectMode["fengDing"] = this.createParams.fengDing;
                tData.areaSelectMode["hongZhuan"] = this.createParams.hongZhuan;
                tData.areaSelectMode["isGenXing"] = this.createParams.isGenXing;
                tData.areaSelectMode["baodi"] = this.createParams.baodi || -1;
                tData.areaSelectMode["minHu"] = "minHu" in this.createParams ? this.createParams.minHu : 15;
                tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
                tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : 0;
                tData.gameCnName = "扯胡子四王";
            }
        }
        else if (tData.gameType == GAME_TYPE.ZP_LY_CHZ) {
            logger.debug("=====GameCode=====runStartGame===========耒阳扯胡子=====================");
            this.pGameCode = gameInstance.pGameCodeZPLYCHZ;
            tData.areaSelectMode["jushou"] = this.createParams.jushou;
            tData.areaSelectMode["budaihu"] = this.createParams.budaihu;
            tData.areaSelectMode["isPutLimit"] = this.createParams.isPutLimit;
            tData.areaSelectMode["budaiyihong"] = this.createParams.budaiyihong;
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.minHuxi = 10;
            tData.areaSelectMode.fanBei = this.createParams.fanBei;//0 不翻倍 1 翻倍
            tData.areaSelectMode.fanBeiScore = this.createParams.fanBeiScore;// 低于多少翻倍
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            if ("zhuangJia" in this.createParams) {
                tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            }
            if (tData.maxPlayer == 2) {
                tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            } else {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime;
            }
            tData.gameCnName = "耒阳字牌";
        }
        else if (tData.gameType == GAME_TYPE.LUO_DI_SAO) {
            logger.debug("=====GameCode=====runStartGame===========永州六胡扫=====================");
            if (utils.isContains(["shaoyang", "shaoyang-test", "dev-a", "dev-b"], env)) {
                this.pGameCode = gameInstance.pGameCodeSYLuoDiSao;
            } else {
                this.pGameCode = gameInstance.pGameCodeYZLHS;
            }
        }
        else if (tData.gameType == GAME_TYPE.RU_GAO) {
            logger.debug("=====GameCode=====runStartGame===========如皋=====================");
            this.pGameCode = gameInstance.pGameCodeRugao;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["huimianchuhan"] = this.createParams.huimianchuhan;
            tData.areaSelectMode["haidilaoyue"] = this.createParams.haidilaoyue;
            tData.areaSelectMode["xijiang"] = this.createParams.xijiang;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["daixi"] = this.createParams.daixi;
            tData.areaSelectMode["shengyihu"] = this.createParams.shengyihu;
            tData.areaSelectMode["jizi"] = this.createParams.jizi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["tiehu"] = this.createParams.tiehu;
            tData.areaSelectMode["qitie"] = this.createParams.qitie;
            tData.areaSelectMode["tiehuCount"] = this.createParams.tiehuCount;
            tData.maxPlayer = 3;
            tData.gameCnName = "长牌";
        }
        else if (tData.gameType == GAME_TYPE.NTHZ) {
            logger.debug("=====GameCode=====runStartGame==========南通红中/十三张麻将===================");
            this.pGameCode = gameInstance.pGameCodeNTHZ;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;
            tData.areaSelectMode["piaofen"] = this.createParams.piaofen;
            tData.areaSelectMode["daihongzhong"] = this.createParams.daihongzhong;
            tData.areaSelectMode["nohongzhongdouble"] = this.createParams.nohongzhongdouble;
            tData.areaSelectMode["mustzimo"] = this.createParams.mustzimo;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.gameCnName = "十三张麻将";
        }
        else if (tData.gameType == GAME_TYPE.CHANG_SHA || tData.gameType == GAME_TYPE.CHANG_SHA_ER_REN) {
            tData.gameType = GAME_TYPE.CHANG_SHA;
            logger.debug("=====GameCode=====runStartGame==========长沙麻将===================");
            this.pGameCode = gameInstance.pGameCodeChangsha;
            tData.areaSelectMode["zhuaNiaoType"] = this.createParams.zhuaNiaoType;// 0算分  1翻倍 2不抓鸟
            tData.areaSelectMode["zhuaNiaoNum"] = this.createParams.zhuaNiaoNum;// 抓鸟数量
            tData.areaSelectMode["zhuaNiaoBuLunKong"] = this.createParams.zhuaNiaoBuLunKong || false;// 抓鸟不轮空
            tData.areaSelectMode["xianPiao"] = this.createParams.xianPiao;// 是否飘分
            tData.areaSelectMode["zhuangXianFeng"] = this.createParams.zhuangXianFeng;// 庄家胡分+1
            tData.areaSelectMode["buBuGao"] = this.createParams.buBuGao;//步步高
            tData.areaSelectMode["jinTongYuNv"] = this.createParams.jinTongYuNv;//金童玉女
            tData.areaSelectMode["sanTong"] = this.createParams.sanTong;//三同
            tData.areaSelectMode["yiZhiHua"] = this.createParams.yiZhiHua;//一枝花
            tData.areaSelectMode["queYiSe"] = this.createParams.queYiSe; // 缺一色
            tData.areaSelectMode["banBanHu"] = this.createParams.banBanHu; // 板板胡
            tData.areaSelectMode["liuLiuShun"] = this.createParams.liuLiuShun; // 六六顺
            tData.areaSelectMode["daSiXi"] = this.createParams.daSiXi; // 大四喜
            tData.areaSelectMode["zhongTuCanAgain"] = this.createParams.zhongTuCanAgain; // 中途六六顺、大四喜可胡多次
            tData.areaSelectMode["zhongTuLiuLiuShun"] = this.createParams.zhongTuLiuLiuShun;//中途六六顺
            tData.areaSelectMode["zhongTuSiXi"] = this.createParams.zhongTuSiXi;//中途四喜
            tData.areaSelectMode["jiaJiangHu"] = this.createParams.jiaJiangHu;//假将胡
            tData.areaSelectMode["menQingZiMo"] = this.createParams.menQingZiMo || false; // 门清自摸
            tData.areaSelectMode["menQing"] = this.createParams.maxPlayer == 2 && !this.createParams.menQingZiMo && this.createParams.menQing ? true : false; // 门清
            tData.areaSelectMode["queYiMen"] = this.createParams.maxPlayer == 2 && !this.createParams.queYiSe && this.createParams.queYiMen ? true : false; // 缺一门
            tData.areaSelectMode["fengDing"] = "fengDing" in this.createParams ? this.createParams.fengDing : -1; // 封顶：-1 不 0：21分 1：42分 2：15分
            tData.areaSelectMode["kai4Gang"] = this.createParams.kai4Gang || false; // 开4杠
            tData.areaSelectMode["changsha_difen"] = this.createParams.changsha_difen || 1;  // 底分
            tData.areaSelectMode["chaHu"] = this.createParams.chaHu;  // 查胡
            tData.areaSelectMode["anzhuang"] = this.createParams.anzhuang;
            tData.areaSelectMode["keqiangzhigang"] = this.createParams.keqiangzhigang; // 可抢直杠
            tData.areaSelectMode["genzhangbudianpao"] = this.createParams.genzhangbudianpao; // 跟张不点炮
            tData.areaSelectMode.isErRen = "isErRen" in this.createParams ? this.createParams.isErRen : false; //二人缺门长麻
            if (tData.areaSelectMode.isErRen) {
                tData.gameCnName = "二人缺门长麻";
            } else {
                tData.gameCnName = "长沙麻将";
            }
        }
        else if (tData.gameType == GAME_TYPE.HENG_YANG_CHANG_SHA) {
            logger.debug("=====GameCode=====runStartGame==========衡阳长沙麻将===================");
            this.pGameCode = gameInstance.pGameCodeHengyangChangsha;
            tData.areaSelectMode["zhuaNiaoType"] = this.createParams.zhuaNiaoType;// 0算分  1翻倍 2不抓鸟
            tData.areaSelectMode["zhuaNiaoNum"] = this.createParams.zhuaNiaoNum;// 抓鸟数量
            // tData.areaSelectMode["xianPiao"] = this.createParams.xianPiao;// 是否飘分
            tData.areaSelectMode["zhuangXianFeng"] = this.createParams.zhuangXianFeng;// 庄家胡分+1
            // tData.areaSelectMode["buBuGao"] = this.createParams.buBuGao;//步步高
            // tData.areaSelectMode["jinTongYuNv"] = this.createParams.jinTongYuNv;//金童玉女
            // tData.areaSelectMode["sanTong"] = this.createParams.sanTong;//三同
            // tData.areaSelectMode["yiZhiHua"] = this.createParams.yiZhiHua;//一枝花
            tData.areaSelectMode["queYiSe"] = this.createParams.queYiSe; // 缺一色
            tData.areaSelectMode["banBanHu"] = this.createParams.banBanHu; // 板板胡
            tData.areaSelectMode["liuLiuShun"] = this.createParams.liuLiuShun; // 六六顺
            tData.areaSelectMode["daSiXi"] = this.createParams.daSiXi; // 大四喜
            tData.areaSelectMode["zhongTuCanAgain"] = this.createParams.zhongTuCanAgain; // 中途六六顺、大四喜可胡多次
            // tData.areaSelectMode["zhongTuLiuLiuShun"] = this.createParams.zhongTuLiuLiuShun;//中途六六顺
            // tData.areaSelectMode["zhongTuSiXi"] = this.createParams.zhongTuSiXi;//中途四喜
            // tData.areaSelectMode["jiaJiangHu"] = this.createParams.jiaJiangHu;//假将胡
            tData.areaSelectMode["menQingZiMo"] = this.createParams.menQingZiMo || false; // 门清自摸
            tData.areaSelectMode["quanQiuRenSifangBaoPei"] = this.createParams.quanQiuRenSifangBaoPei || false; // 全球人四放包赔
            // tData.areaSelectMode["fengDing"] = "fengDing" in this.createParams ? this.createParams.fengDing : -1; // 封顶：-1 不 0：21分 1：42分
            tData.areaSelectMode["changsha_difen"] = this.createParams.changsha_difen || 1;  // 底分
            tData.gameCnName = "258麻将";
        }
        else if (tData.gameType == GAME_TYPE.XIANG_YIN_TUI_DAO_HU) {
            logger.debug("=====GameCode=====runStartGame==========湘阴推倒胡===================", JSON.stringify(this.createParams));
            this.pGameCode = gameInstance.pGameCodeXiangyinTDH;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;
            tData.areaSelectMode["jiapiao"] = this.createParams.jiapiao;// 是否飘分
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;// 抓鸟
            tData.areaSelectMode["jiangjianghu"] = this.createParams.jiangjianghu;// 将将胡
            tData.areaSelectMode["eatqinyise"] = this.createParams.eatqinyise;// 清一色可吃
            tData.areaSelectMode["minggangguogangbunengbu"] = this.createParams.minggangguogangbunengbu;
            tData.areaSelectMode["genzhangbudianpao"] = this.createParams.genzhangbudianpao;
            tData.areaSelectMode["pphjpbsjjh"] = this.createParams.pphjpbsjjh;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;  // 底分
            tData.areaSelectMode["fengding"] = this.createParams.fengding || 120;
            tData.areaSelectMode["trustWay"] = "trustWay" in this.createParams ? this.createParams.trustWay : 0;
            tData.gameCnName = "湘阴推倒胡";
        }
        else if (tData.gameType == GAME_TYPE.PING_JIANG_ZHA_NIAO) {
            logger.debug("=====GameCode=====runStartGame==========平江扎鸟===================");
            this.pGameCode = gameInstance.pGameCodePingjiangZhaniao;
            // tData.areaSelectMode["jiapiao"] = this.createParams.jiapiao;// 是否飘分
            // tData.areaSelectMode["jiangjianghu"] = this.createParams.jiangjianghu;// 将将胡
            // tData.areaSelectMode["eatqinyise"] = this.createParams.eatqinyise;// 清一色可吃
            // tData.areaSelectMode["minggangguogangbunengbu"] = this.createParams.minggangguogangbunengbu;
            // tData.areaSelectMode["genzhangbudianpao"] = this.createParams.genzhangbudianpao;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;// 抓鸟
            tData.areaSelectMode["zhaniaoRule"] = this.createParams.zhaniaoRule;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["guding2zhang"] = this.createParams.guding2zhang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;  // 底分
            tData.areaSelectMode["zhuang"] = this.createParams.zhuang;
            tData.gameCnName = "平江扎鸟";
        }
        else if (tData.gameType == GAME_TYPE.GAN_YU) {
            logger.debug("=====GameCode=====runStartGame===========赣榆=====================");
            this.pGameCode = gameInstance.pGameCodeganyu;
            tData.areaSelectMode["tingType"] = this.createParams.tingType;//听牌类型，TingCardType的枚举，默认不可以听牌
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;//截胡
            tData.areaSelectMode["canChi"] = this.createParams.canChi;//是否可吃
            tData.areaSelectMode["flowerCount"] = this.createParams.flowerCount;//花数
            tData.areaSelectMode["flowerType"] = this.createParams.flowerType;//花牌
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;//底分
            //tData.areaSelectMode["withFlowerType"] = this.createParams.;//选择花的类型 WithFlowerType的枚举，默认无花
            tData.gameCnName = "赣榆";
        }
        else if (tData.gameType == GAME_TYPE.RU_GAO_MJH) {
            logger.debug("=====GameCode=====runStartGame===========如皋麻将胡=====================");
            this.pGameCode = gameInstance.pGameCodeRugaoMJH;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["canHuCount"] = this.createParams.canHuCount;
            tData.areaSelectMode["tiehu"] = this.createParams.tiehu;
            tData.areaSelectMode["qitie"] = this.createParams.qitie;
            tData.areaSelectMode["tiehuCount"] = this.createParams.tiehuCount;
            tData.maxPlayer = 3;
            tData.gameCnName = "麻将胡";
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI) {
            logger.debug("=====GameCode=====runStartGame===========跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuai;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_TY || tData.gameType == GAME_TYPE.YZ_PAO_DE_KUAI_TY) {
            logger.debug("=====GameCode=====runStartGame===========通用跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiTY;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_HBTY) {
            logger.debug("=====GameCode=====runStartGame===========湖北通用跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiHBTY;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_XU_ZHOU) {
            logger.debug("=====GameCode=====runStartGame===========徐州跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiXuzhou;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_HA) {
            logger.debug("=====GameCode=====runStartGame===========淮安跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiHuaian;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_HUAIAN_NEW) {
            logger.debug("=====GameCode=====runStartGame===========跑得快(淮安新跑得快)=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiHuaianNew;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_LYG) {
            logger.debug("=====GameCode=====runStartGame===========连云港跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiLyg;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_JZ) {
            logger.debug("=====GameCode=====runStartGame===========晋中跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiJZ;
        }
        else if (tData.gameType == GAME_TYPE.SHAN_XI_GAN_DENG_YAN) {
            logger.debug("=====GameCode=====runStartGame===========山西干瞪眼=====================");
            this.pGameCode = gameInstance.pGameCodeGanDengYanSX;
        }
        else if (tData.gameType == GAME_TYPE.WU_XUE_GE_BAN) {
            logger.debug("=====GameCode=====runStartGame===========武穴隔板=====================");
            this.pGameCode = gameInstance.pGameCodeWuXueGeBan;
        }
        else if (tData.gameType == GAME_TYPE.CHONG_YANG_DA_GUN) {
            logger.debug("=====GameCode=====runStartGame===========崇阳打滚=====================");
            this.pGameCode = gameInstance.pGameCodeChongYangDaGun;
        }
        else if (tData.gameType == GAME_TYPE.WU_XUE_510K) {
            logger.debug("=====GameCode=====runStartGame===========武穴510k=====================");
            this.pGameCode = gameInstance.pGameCodeWuXue510K;
        }
        else if (tData.gameType == GAME_TYPE.TONG_SHAN_DA_GONG) {
            logger.debug("=====GameCode=====runStartGame===========通山打拱=====================");
            this.pGameCode = gameInstance.pGameCodeTongShanDaGong;
        }
        else if (tData.gameType == GAME_TYPE.DA_YE_510K) {
            logger.debug("=====GameCode=====runStartGame===========大冶510K=====================");
            this.pGameCode = gameInstance.pGameCodeDaYe510K;
        }
        else if (tData.gameType == GAME_TYPE.CHONG_YANG_HUA_QUAN_JIAO) {
            logger.debug("=====GameCode=====runStartGame===========崇阳画圈脚=====================");
            this.pGameCode = gameInstance.pGameCodeHuaQuanJiao;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_NT) {
            logger.debug("=====GameCode=====runStartGame===========南通斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuNT;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_HBTY) {
            logger.debug("=====GameCode=====runStartGame===========湖北通用斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuHBTY;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_QC) {
            logger.debug("=====GameCode=====runStartGame===========湖北通用斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuQC;
        }
        else if (tData.gameType == GAME_TYPE.LIAN_SHUI) {
            logger.debug("=====GameCode=====runStartGame===========涟水麻将===================");
            this.pGameCode = gameInstance.pGameCodeLianshui;
            tData.areaSelectMode["flowerType"] = this.createParams.flowerType;//花牌
            tData.areaSelectMode["baoZhuangType"] = this.createParams.baoZhuangType;//包庄
            tData.areaSelectMode["zhuangTing"] = this.createParams.zhuangTing;//大包装是否听牌
            tData.areaSelectMode["difen"] = this.createParams.difen;// 底分
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "淮安涟水";
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_TY) {
            logger.debug("=====GameCode=====runStartGame===========通用斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuTY;
        }
        else if (tData.gameType == GAME_TYPE.LEI_YANG_GMJ) {
            logger.debug("=====GameCode=====runStartGame===========耒阳鬼麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLeiYangG;
            tData.areaSelectMode["guipaiType"] = this.createParams.guipaiType; // (0.翻鬼 1.红中鬼,2上下鬼)
            tData.areaSelectMode["zhuaniaoNum"] = this.createParams.zhuaniaoNum; // 抓鸟(1, 2, 4, 6)
            tData.areaSelectMode["shaguiType"] = this.createParams.shaguiType; // (0.不杀鬼 1.杀鬼2番 2. 杀鬼+5分)
            tData.areaSelectMode["isQishouhu"] = this.createParams.isQishouhu; // 是否4鬼起手胡
            tData.areaSelectMode["isQiangganghu"] = this.createParams.isQiangganghu; // 能否抢杠胡
            tData.areaSelectMode["isQianggangquanbao"] = this.createParams.isQianggangquanbao; // 是否抢杠全包
            tData.areaSelectMode["wuguiType"] = "wuguiType" in this.createParams ? this.createParams.wuguiType : 0;//(0.无鬼不翻倍 1.无鬼翻2倍 2.无鬼翻3倍)
            tData.areaSelectMode["diFen"] = this.createParams.diFen; // / 低分翻倍 -1 不翻倍 5-10...-100 x分
            tData.areaSelectMode["jieSuanDiFen"] = this.createParams.jieSuanDiFen;// 结算底分
            if ("zhuangJia" in this.createParams) {
                tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            }
            tData.gameCnName = "耒阳鬼麻将";
        }
        else if (tData.gameType == GAME_TYPE.TY_HONGZHONG) {
            if (utils.isContains(['shaoyang', 'shaoyang-test', 'dev-a', 'dev-b'], env) || "shaoyang" == this.createParams.local_env) {
                logger.debug("=====GameCode=====runStartGame===========通用红中(邵阳)=====================");
                this.pGameCode = gameInstance.pGameCodeTYHZ_ShaoYang;
            }
            else if (utils.isContains(['yongzhou', 'yongzhou-test'], env)) {
                logger.debug("=====GameCode=====runStartGame===========通用红中(永州)=====================");
                this.pGameCode = gameInstance.pGameCodeTYHZ_YongZhou;
            } else {
                this.pGameCode = gameInstance.pGameCodeTYHZ_ShaoYang;
            }
        }
        else if (tData.gameType == GAME_TYPE.XIANG_XIANG_HONG_ZHONG) {
            logger.debug("=====GameCode=====runStartGame===========湘乡红中=====================");
            this.pGameCode = gameInstance.pGameCodeXiangXiangHongZhong;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;
            tData.areaSelectMode["niaofen"] = this.createParams.niaofen || 1;  // 鸟分底分
            tData.areaSelectMode["zhuangxianfen"] = this.createParams.zhuangxianfen;
            tData.areaSelectMode["dianpao"] = this.createParams.dianpao;
            tData.areaSelectMode["qianggang"] = this.createParams.qianggang; //0:无 1:抢杠算自摸 2:抢杠算点炮 兼容原 false:无 true:抢杠胡
            tData.areaSelectMode["qianggangquanbao"] = (this.createParams.qianggang == 1 || this.createParams.qianggang === true) && this.createParams.qianggangquanbao;
            tData.areaSelectMode["hongzhong8"] = this.createParams.hongzhong8;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["youhongzhongbujiepao"] = this.createParams.youhongzhongbujiepao;
            tData.areaSelectMode["wuhongzhongjiabei"] = this.createParams.wuhongzhongjiabei;
            tData.areaSelectMode["duoHu"] = true;
            tData.areaSelectMode["zuoZhuang"] = "zuoZhuang" in this.createParams ? this.createParams.zuoZhuang : 1;//首局坐庄  0：随机庄   1：房主庄
            tData.areaSelectMode["niaofen"] = tData.areaSelectMode.niaofen || 1;
            tData.areaSelectMode["bihuType"] = "bihuType" in this.createParams ? this.createParams.bihuType : false;
            tData.areaSelectMode["genzhangbudianpao"] = this.createParams.genzhangbudianpao;
            tData.areaSelectMode["liuniaowanfa"] = (this.createParams.zhuaniao == 6 && this.createParams.liuniaowanfa) || false; // 6鸟玩法（不中算全中，全中翻倍）
            tData.areaSelectMode["wuhongzhong"] = "wuhongzhong" in this.createParams ? this.createParams.wuhongzhong : true;  //有红中可抢杠胡
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["trustTime"] = "trustTime" in this.createParams ? this.createParams.trustTime : 0;// 是否托管
            tData.gameCnName = "红中麻将";
        }
        else if (tData.gameType == GAME_TYPE.TY_ZHUANZHUAN) {
            this.pGameCode = gameInstance.pGameCodeTYZhuanzhuan;
        }
        else if (tData.gameType == GAME_TYPE.SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========岳阳三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerYueYangSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.BAI_PU_LIN_ZI) {
            logger.debug("=====GameCode=====runStartGame===========白蒲林梓长牌=====================");
            this.pGameCode = gameInstance.pGameCodeBaipulinziChangpai;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["huimianchuhan"] = this.createParams.huimianchuhan;
            tData.areaSelectMode["haidilaoyue"] = this.createParams.haidilaoyue;
            tData.areaSelectMode["xijiang"] = this.createParams.xijiang;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["daixi"] = this.createParams.daixi;
            tData.areaSelectMode["shengyihu"] = this.createParams.shengyihu;
            tData.areaSelectMode["pigu"] = this.createParams.pigu;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["tiehu"] = this.createParams.tiehu;
            tData.areaSelectMode["qitie"] = this.createParams.qitie;
            tData.areaSelectMode["tiehuCount"] = this.createParams.tiehuCount;
            tData.maxPlayer = 3;
            tData.gameCnName = "白蒲林梓长牌";
        }
        else if (tData.gameType == GAME_TYPE.HY_LIU_HU_QIANG) { // 衡阳六胡抢
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if (this.createParams.fieldCountdown > 0) {
                tData.areaSelectMode.trustTime = this.createParams.fieldCountdown + 1;
            }

            this.pGameCode = gameInstance.pGameCodeHYZP;
            if (tData.areaSelectMode.trustTime != -1) { // 托管重构流程
                this.pGameCode = gameInstance.pGameCodeLiuHuQiang;
                this.pGameCode.initTable(this); // 初始化tData
            }
            tData.areaSelectMode["bihuType"] = this.createParams.bihuType == undefined ? 0 : this.createParams.bihuType; // (0.有胡必胡 1.点炮必胡)
            tData.areaSelectMode["xiNum"] = tData.minHuxi = this.createParams.xiNum; // (起胡胡息 6 9 15)
            tData.areaSelectMode["xingType"] = this.createParams.xingType; // (0不带省 1跟省 2翻省)
            tData.areaSelectMode["suanfenType"] = this.createParams.suanfenType; // (0.底分1分 1.底分2分 2.1息1囤)
            tData.areaSelectMode["isMingwei"] = this.createParams.isMingwei; // 是否明偎
            tData.areaSelectMode["isYiwushi"] = this.createParams.isYiwushi; // 是否有一五十
            tData.areaSelectMode["isHongheidian"] = this.createParams.isHongheidian; // 是否有红黑点
            tData.areaSelectMode["isTiandihu"] = this.createParams.isTiandihu || false; // 是否有天地胡
            tData.areaSelectMode["isTianhu"] = this.createParams.isTianhu || false; // 是否有天胡
            tData.areaSelectMode["isDihu"] = this.createParams.isDihu || false; // 是否有地胡
            tData.areaSelectMode["is21Zhang"] = false; // 是否21张
            if (tData.maxPlayer == 3 || tData.maxPlayer == 2) {
                tData.areaSelectMode["is21Zhang"] = this.createParams.is21Zhang || false;
            }
            if (tData.areaSelectMode.isTiandihu) {
                tData.areaSelectMode.isTianhu = true;
                tData.areaSelectMode.isDihu = true;
            }
            tData.areaSelectMode.isTiandihu = false;

            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;

            if (tData.maxPlayer == 2) {
                tData.areaSelectMode["isMaiPai"] = "isMaiPai" in this.createParams ? this.createParams.isMaiPai : true;
            }
            tData.areaSelectMode.isHuLiangZhang = "isHuLiangZhang" in this.createParams ? this.createParams.isHuLiangZhang : true; // 是否胡亮张
            tData.areaSelectMode.diFen = "diFen" in this.createParams ? this.createParams.diFen : -1; // 分低于多少翻倍(-1.不翻 10.10分 20.20分 30.30分)
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            //新增埋牌
            if (tData.maxPlayer == 2) {
                tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            } else {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            if (!tData.areaSelectMode.isMaiPai) {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            if ("zhuangJia" in this.createParams) {
                tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            }
            tData.gameCnName = "衡阳六胡抢";
        }
        else if (tData.gameType == GAME_TYPE.HY_SHI_HU_KA) { // 衡阳十胡卡
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if (this.createParams.fieldCountdown > 0) {
                tData.areaSelectMode.trustTime = this.createParams.fieldCountdown + 1;
            }

            this.pGameCode = gameInstance.pGameCodeHYZP;
            if (tData.areaSelectMode.trustTime != -1) { // 托管重构流程
                this.pGameCode = gameInstance.pGameCodeShiHuKa;
                this.pGameCode.initTable(this); // 初始化tData
            }

            tData.areaSelectMode["bihuType"] = this.createParams.bihuType; // (0.有胡必胡 1.点炮必胡 2.无必胡)
            tData.areaSelectMode["xingType"] = this.createParams.xingType; // (0不带省 1跟省 2翻省)
            tData.areaSelectMode["isErfen"] = this.createParams.isErfen; // 是否底分2分
            tData.areaSelectMode["isYiwushi"] = this.createParams.isYiwushi; // 是否有一五十
            tData.areaSelectMode["isHongheidian"] = this.createParams.isHongheidian == undefined ? true : this.createParams.isHongheidian; // 是否有红黑点
            tData.areaSelectMode["isHaidihu"] = this.createParams.isHaidihu; // 是否有海底胡
            tData.areaSelectMode["hongziType"] = this.createParams.hongziType || 0; // 0.大小红 1.10红3倍多一红+3胡
            tData.areaSelectMode["fangpaoType"] = this.createParams.fangpaoType || 0; // 0.3倍 1.2倍 2: 1倍
            tData.areaSelectMode["isTiandihu"] = this.createParams.isTiandihu || false; // 是否有天地胡
            tData.areaSelectMode["isTianhu"] = this.createParams.isTianhu || false; // 是否有天胡
            tData.areaSelectMode["isDihu"] = this.createParams.isDihu || false; // 是否有地胡
            tData.areaSelectMode["isPiaoHu"] = this.createParams.isPiaoHu || false; // 是否有飘胡
            tData.areaSelectMode["isZiMoFanBei"] = this.createParams.isZiMoFanBei === undefined ? true : this.createParams.isZiMoFanBei; // 是否自摸翻倍
            if (tData.areaSelectMode.isTiandihu) {
                tData.areaSelectMode.isTianhu = true;
                tData.areaSelectMode.isDihu = true;
            }
            tData.areaSelectMode.isTiandihu = false;
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui || false; // 是否有加锤

            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode["isYiHongSanBei"] = this.createParams.isYiHongSanBei || false; // 是否一点红三倍
            // if (tData.maxPlayer == 2) {
            //     tData.areaSelectMode["isMaiPai"] = "isMaiPai" in this.createParams ? this.createParams.isMaiPai : true;
            // }
            tData.areaSelectMode.isHuLiangZhang = "isHuLiangZhang" in this.createParams ? this.createParams.isHuLiangZhang : true; // 是否胡亮张
            tData.areaSelectMode.diFen = "diFen" in this.createParams ? this.createParams.diFen : -1; // 分低于多少翻倍(-1.不翻 10.10分 20.20分 30.30分)
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            //新增埋牌
            // if(tData.maxPlayer == 2) {
            //     tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            // }else {
            //     tData.areaSelectMode.maiPaiNum = 0;
            // }
            // if(!tData.areaSelectMode.isMaiPai) {
            //     tData.areaSelectMode.maiPaiNum = 0;
            // }

            tData.areaSelectMode.maiPaiNum = [0, 10, 20][this.createParams.maiPaiType || 0];          // 埋牌20张

            if ("zhuangJia" in this.createParams) {
                tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            }
            tData.minHuxi = 10;
            tData.gameCnName = "衡阳十胡卡";
        }
        else if (tData.gameType == GAME_TYPE.RU_GAO_SHUANG_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========双将长牌=====================");
            this.pGameCode = gameInstance.pGameCodeShuangjiangChangpai;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["huimianchuhan"] = this.createParams.huimianchuhan;
            tData.areaSelectMode["haidilaoyue"] = this.createParams.haidilaoyue;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["daixi"] = this.createParams.daixi;
            tData.areaSelectMode["jizi"] = this.createParams.jizi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["sanlaohuimian"] = this.createParams.sanlaohuimian;
            tData.areaSelectMode["tiehu"] = this.createParams.tiehu;
            tData.areaSelectMode["qitie"] = this.createParams.qitie;
            tData.areaSelectMode["tiehuCount"] = this.createParams.tiehuCount;
            tData.maxPlayer = 3;
            tData.gameCnName = "双将长牌";
        }
        else if (tData.gameType == GAME_TYPE.XUE_LIU) {
            logger.debug("=====GameCode=====runStartGame===========血流=====================");
            this.pGameCode = gameInstance.pGameCodeXueliu;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["jingoudiao"] = this.createParams.jingoudiao;
            tData.areaSelectMode["difen"] = this.createParams.difen;
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "血流";
        }
        else if (tData.gameType == GAME_TYPE.XUE_ZHAN) {
            logger.debug("=====GameCode=====runStartGame===========血战=====================");
            this.pGameCode = gameInstance.pGameCodeXuezhan;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["jingoudiao"] = this.createParams.jingoudiao;
            tData.areaSelectMode["difen"] = this.createParams.difen;
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "血战";
        }
        else if (tData.gameType == GAME_TYPE.HAI_AN_MJ) {
            logger.debug("=====GameCode=====runStartGame===========海安=====================");
            this.pGameCode = gameInstance.pGameCodeHaian;
            tData.areaSelectMode["lazi"] = this.createParams.lazi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["baozi"] = this.createParams.baozi;
            tData.areaSelectMode["duoHu"] = true;
            tData.areaSelectMode["canChi"] = this.createParams.canChi;
            tData.areaSelectMode["biHu"] = this.createParams.biHu;
            tData.gameCnName = "海安";
        }
        else if (tData.gameType == GAME_TYPE.HAI_AN_BAI_DA) {
            logger.debug("=====GameCode=====runStartGame===========海安百搭=====================");
            this.pGameCode = gameInstance.pGameCodeHaianBaida;
            tData.areaSelectMode["lazi"] = this.createParams.lazi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["baozi"] = this.createParams.baozi;
            tData.areaSelectMode["gangchong"] = this.createParams.gangchong;
            tData.areaSelectMode["qianggang"] = this.createParams.qianggang;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["menqing"] = this.createParams.menqing;
            tData.areaSelectMode["duoHu"] = true;
            tData.gameCnName = "海安百搭";
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_NT) {
            logger.debug("=====GameCode=====runStartGame===========南通跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiNT;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_HAIAN) {
            logger.debug("=====GameCode=====runStartGame===========海安跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiHaian;
        }
        else if (tData.gameType == GAME_TYPE.YONG_ZHOU_MJ) {
            logger.debug("=====GameCode=====runStartGame===========永州麻将=====================");
            this.pGameCode = gameInstance.pGameCodeYZMaJiang;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;//抓鸟数
            tData.areaSelectMode["caneat"] = this.createParams.caneat;//是否可以吃
            tData.areaSelectMode["gangsuihu"] = this.createParams.gangsuihu;//杠随胡
            tData.areaSelectMode["huType"] = this.createParams.huType || 0;//胡牌类型
            tData.areaSelectMode["openType"] = this.createParams.openType || 1;//开放类型
            tData.gameCnName = "永州麻将";
        }
        else if (tData.gameType == GAME_TYPE.ML_HONGZHONG) {
            logger.debug("=====GameCode=====runStartGame===========红中麻将=====================");
            this.pGameCode = gameInstance.pGameCodeMLHZ;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;
            tData.areaSelectMode["buzhongsuanquanzhong"] = this.createParams.buzhongsuanquanzhong;
            tData.areaSelectMode["dianpao"] = this.createParams.dianpao;
            tData.areaSelectMode["qianggang"] = this.createParams.qianggang;
            tData.areaSelectMode["qianggangquanbao"] = this.createParams.qianggangquanbao;
            tData.areaSelectMode["hongzhong8"] = this.createParams.hongzhong8;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["youhongzhongbujiepao"] = this.createParams.youhongzhongbujiepao;
            tData.areaSelectMode["youhongzhongkeqiangganghu"] = this.createParams.youhongzhongkeqiangganghu;
            tData.areaSelectMode["whzjiabeiType"] = this.createParams.whzjiabeiType;
            tData.areaSelectMode["wuhongzhongjia2niao"] = this.createParams.wuhongzhongjia2niao;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["hongzhongkehu"] = this.createParams.hongzhongkehu;
            tData.areaSelectMode["niaofen"] = this.createParams.niaofen || 2;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.areaSelectMode["jiapiao"] = this.createParams.jiapiao;
            tData.areaSelectMode["zhuangxianfen"] = this.createParams.zhuangxianfen;
            tData.areaSelectMode["bihuType"] = this.createParams.bihuType;
            tData.areaSelectMode["qdpphthqysjf"] = this.createParams.qdpphthqysjf;
            tData.areaSelectMode["quanzhongfanbei"] = this.createParams.quanzhongfanbei;
            tData.areaSelectMode["piaoniao"] = this.createParams.piaoniao;  //围一飘鸟
            tData.areaSelectMode["dianpaofen"] = this.createParams.dianpaofen;//点炮分
            tData.areaSelectMode["zuoZhuang"] = this.createParams.zuoZhuang;//坐庄
            tData.areaSelectMode["huQingYiSeQiDuiJia2Niao"] = this.createParams.huQingYiSeQiDuiJia2Niao;//胡清一色七对加2鸟
            tData.areaSelectMode["huangZhuangGang"] = this.createParams.huangZhuangGang;//荒庄荒杠
            tData.areaSelectMode["buKePeng"] = this.createParams.buKePeng;//不可碰
            tData.areaSelectMode["zhongHongZhongSuan12"] = this.createParams.zhongHongZhongSuan12;//中红中算12
            tData.areaSelectMode["zimo"] = this.createParams.zimo || 2;
            tData.areaSelectMode["trustWay"] = "trustWay" in this.createParams ? this.createParams.trustWay : 0;
            tData.gameCnName = "红中麻将";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========晋中=====================");
            this.pGameCode = gameInstance.pGameCodeJinzhong;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["guohuzhinenglongpai"] = this.createParams.guohuzhinenglongpai;
            tData.areaSelectMode["passhudapai"] = this.createParams.passhudapai;
            tData.areaSelectMode["calltingautohu"] = this.createParams.calltingautohu;
            tData.areaSelectMode["is68"] = this.createParams.is68;
            tData.areaSelectMode["maxPlayer"] = this.createParams.maxPlayer;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "晋中麻将";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_CAI_SHEN) {
            logger.debug("=====GameCode=====runStartGame===========晋中财神=====================");
            this.pGameCode = gameInstance.pGameCodeJinzhongCaishen;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["guohuzhinenglongpai"] = this.createParams.guohuzhinenglongpai;
            tData.areaSelectMode["passhudapai"] = this.createParams.passhudapai;
            tData.areaSelectMode["calltingautohu"] = this.createParams.calltingautohu;
            tData.areaSelectMode["is68"] = this.createParams.is68;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["maxPlayer"] = this.createParams.maxPlayer;
            tData.gameCnName = "晋中财神";
        }
        else if (tData.gameType == GAME_TYPE.LING_SHI_BAN_MO) {
            logger.debug("=====GameCode=====runStartGame===========灵石半摸=====================");
            this.pGameCode = gameInstance.pGameCodeLingshiBanmo;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "灵石半摸";
        }
        else if (tData.gameType == GAME_TYPE.LING_SHI_BIAN_LONG) {
            logger.debug("=====GameCode=====runStartGame===========灵石编龙=====================");
            this.pGameCode = gameInstance.pGameCodeLingshiBianlong;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["guohuzhinenglongpai"] = this.createParams.guohuzhinenglongpai;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "灵石编龙";
        }
        else if (tData.gameType == GAME_TYPE.SHOU_YANG_QUE_KA) {
            logger.debug("=====GameCode=====runStartGame===========寿阳缺卡=====================");
            this.pGameCode = gameInstance.pGameCodeShouyangQueka;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "寿阳缺卡";
        }
        else if (tData.gameType == GAME_TYPE.PING_YAO_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========平遥麻将=====================");
            this.pGameCode = gameInstance.pGameCodePingyaoMJ;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "平遥麻将";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_KD) {
            logger.debug("=====GameCode=====runStartGame===========晋中扣点=====================");
            this.pGameCode = gameInstance.pGameCodeJinzhongKoudian;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["biHu"] = this.createParams.biHu;
            tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["haohuaqidui"] = this.createParams.haohuaqidui;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，1 不带风，2 带耗子  3 随机耗子 4 双耗子
            tData.areaSelectMode["zhuangfen"] = this.createParams.zhuangfen;
            tData.areaSelectMode["zhuangfenzimofanfan"] = this.createParams.zhuangfenzimofanfan;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["xuanzuo"] = this.createParams.xuanzuo;
            tData.gameCnName = "晋中扣点";
        }
        else if (tData.gameType == GAME_TYPE.LV_LIANG_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========吕梁扣点=====================");
            this.pGameCode = gameInstance.pGameCodeLvliangKoudian;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["playType"] = this.createParams.playType;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "吕梁扣点";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_GUAI_SAN_JIAO) {
            logger.debug("=====GameCode=====runStartGame===========拐三角=====================");
            this.pGameCode = gameInstance.pGameCodeGuaisanjiao;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;//一炮多响
            tData.areaSelectMode["gangsuihu"] = this.createParams.gangsuihu;//杠随胡
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;//十三幺
            tData.gameCnName = "拐三角";
        }
        else if (tData.gameType == GAME_TYPE.PING_YAO_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========平遥扣点=====================");
            this.pGameCode = gameInstance.pGameCodePingyaoKoudian;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["guozi"] = this.createParams.guozi;
            tData.areaSelectMode["haozi"] = this.createParams.haozi;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "平遥扣点";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_TUI_DAO_HU) {
            logger.debug("=====GameCode=====runStartGame===========晋中推倒胡=====================");
            this.pGameCode = gameInstance.pGameCodeJinzhongTDH;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.areaSelectMode["baoting"] = this.createParams.baoting;
            tData.areaSelectMode["daifeng"] = this.createParams.daifeng;
            tData.areaSelectMode["mustzimo"] = this.createParams.mustzimo;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.areaSelectMode["gangsuihuzou"] = this.createParams.gangsuihuzou;
            tData.areaSelectMode["queyimen"] = this.createParams.queyimen;
            tData.areaSelectMode["youhubihu"] = this.createParams.youhubihu;
            tData.areaSelectMode["yizhangying"] = this.createParams.yizhangying;
            tData.gameCnName = "晋中推倒胡";
        }
        else if (tData.gameType == GAME_TYPE.JIE_XIU_1_DIAN_3) {
            logger.debug("=====GameCode=====runStartGame===========介休1点3=====================");
            this.pGameCode = gameInstance.pGameCodeJiexiuMJ;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["baoting"] = this.createParams.baoting;
            tData.areaSelectMode["daizhuang"] = this.createParams.daizhuang;
            tData.areaSelectMode["daifeng"] = this.createParams.daifeng;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.areaSelectMode["mustzimo"] = this.createParams.mustzimo;
            tData.areaSelectMode["dianpaoshaogang"] = this.createParams.dianpaoshaogang;
            tData.areaSelectMode["gangsuihuzou"] = this.createParams.gangsuihuzou;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "介休1点3";
        }
        else if (tData.gameType == GAME_TYPE.JIE_XIU_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========介休扣点=====================");
            this.pGameCode = gameInstance.pGameCodeJiexiuKoudian;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["guozi"] = this.createParams.guozi;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "介休扣点";
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_JZ) {
            logger.debug("=====GameCode=====runStartGame===========晋中斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuJZ;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_ZERO) {
            logger.debug("=====GameCode=====runStartGame===========单机斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuZero;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_ZERO) {
            logger.debug("=====GameCode=====runStartGame===========单机跑得快=====================");
            this.pGameCode = gameInstance.pGameCodePaodekuaiTYZero;
        }
        else if (tData.gameType == GAME_TYPE.ML_HONGZHONG_ZERO) {
            logger.debug("=====GameCode=====runStartGame===========单机汨罗红中=====================");
            this.pGameCode = gameInstance.pGameCodeMLHZZero;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_DA_TONG) {
            logger.debug("=====GameCode=====runStartGame===========大同斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuDaTong;
        }
        else if (tData.gameType == GAME_TYPE.HONG_TONG_WANG_PAI) {
            logger.debug("=====GameCode=====runStartGame===========洪洞王牌=====================");
            this.pGameCode = gameInstance.pGameCodeHongtongWangpai;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["wanfa"] = this.createParams.wanfa;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["mianpeng"] = this.createParams.mianpeng;
            tData.areaSelectMode["daiwangqingyise"] = this.createParams.daiwangqingyise;
            tData.areaSelectMode["gangsuihu"] = this.createParams.gangsuihu;//杠随胡
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "洪洞王牌";
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_HA) {
            logger.debug("=====GameCode=====runStartGame===========海安斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuHA;
        }
        else if (tData.gameType == GAME_TYPE.JIANG_HUA_MJ) {
            logger.debug("=====GameCode=====runStartGame===========江华麻将=====================");
            this.pGameCode = gameInstance.pGameCodeJHMaJiang;
            tData.areaSelectMode["songma"] = this.createParams.songma;//抓鸟数
            tData.areaSelectMode["dianpaohu"] = this.createParams.dianpaohu;//点炮胡
            tData.areaSelectMode["caneat"] = this.createParams.caneat;//是否可以吃
            tData.areaSelectMode["gangsuihu"] = this.createParams.gangsuihu;//杠随胡
            tData.areaSelectMode["daxiaodao"] = this.createParams.daxiaodao;//大小道
            tData.areaSelectMode["youtao"] = this.createParams.youtao;//有套
            tData.areaSelectMode["fengding"] = this.createParams.fengding;//封顶
            tData.gameCnName = "江华麻将";
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_YI_JIAO_LAI_YOU) {
            logger.debug("=====GameCode=====runStartGame===========一脚赖油=====================");
            this.pGameCode = gameInstance.pGameCodeYiJiaoLaiYou;
        }
        else if (tData.gameType == GAME_TYPE.HUAI_AN_DOU_DI_ZHU) {
            logger.debug("=====GameCode=====runStartGame===========淮安斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuHuaiAn;
        }
        else if (tData.gameType == GAME_TYPE.JIANG_YONG_15Z) {
            logger.debug("=====GameCode=====runStartGame===========江永15张=====================");
            this.pGameCode = gameInstance.pGameCodeJY15Z;
            tData.areaSelectMode["kingNum"] = 2;
            tData.areaSelectMode["isFanXing"] = 1;
            tData.areaSelectMode["fengDing"] = this.createParams.fengDing;
            tData.areaSelectMode["isGenXing"] = this.createParams.isGenXing;
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : 0;
            tData.gameCnName = "江永15张";
        }
        else if (tData.gameType == GAME_TYPE.DAO_ZHOU_MJ) {
            logger.debug("=====GameCode=====runStartGame===========道州麻将=====================");
            this.pGameCode = gameInstance.pGameCodeDaoZhouMJ_ins;
            tData.areaSelectMode["songma"] = 0;//抓鸟数
            tData.areaSelectMode["dianpaohu"] = this.createParams.dianpaohu;//点炮胡
            tData.areaSelectMode["caneat"] = this.createParams.caneat;//是否可以吃
            tData.areaSelectMode["gangsuihu"] = false;//杠随胡
            tData.areaSelectMode["daxiaodao"] = this.createParams.daxiaodao;//大小道
            tData.areaSelectMode["youtao"] = false;//有套(取反，兼容之前的版本)
            tData.areaSelectMode["fengding"] = this.createParams.fengding;//封顶
            tData.areaSelectMode["kepiao"] = this.createParams.kepiao;//下飘
            tData.areaSelectMode["hunyise"] = this.createParams.hunyise;//混一色
            tData.areaSelectMode["zuopiao"] = this.createParams.zuopiao || 0;//坐飘
            //坐飘分数修改标识(创建房间时生成)
            tData.areaSelectMode["zuopiaoChange"] = "zuopiaoChange" in this.createParams ? this.createParams.zuopiaoChange : false;
            if (!tData.areaSelectMode.zuopiaoChange && utils.isContains("shaoyang", "shaoyang-test", "dev-a", env)) {
                var zuopiaoArray = [0, 1, 3, 5];
                var zuopiaoIndex = zuopiaoArray.indexOf[tData.areaSelectMode.zuopiao];
                tData.areaSelectMode.zuopiao = [0, 1, 2, 3][zuopiaoIndex];
            }
            tData.areaSelectMode["mingzhua"] = this.createParams.mingzhua || false  //王抓
            tData.areaSelectMode["zhuaMa"] = "zhuaMa" in this.createParams ? this.createParams.zhuaMa : 0;  //抓码
            tData.gameCnName = "道州麻将";
        }
        else if (tData.gameType == GAME_TYPE.RU_DONG_SHUANG_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========如东双将=====================");
            this.pGameCode = gameInstance.pGameCodeShuangjiangRudong;
            tData.areaSelectMode["duoHu"] = false;
            // tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["huimianchuhan"] = this.createParams.huimianchuhan;
            tData.areaSelectMode["haidilaoyue"] = this.createParams.haidilaoyue;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["daixi"] = this.createParams.daixi;
            tData.areaSelectMode["jizi"] = this.createParams.jizi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["sanlaohuimian"] = this.createParams.sanlaohuimian;
            if (tData.areaSelectMode["daixi"]) {
                tData.areaSelectMode["qinghudaixifan"] = this.createParams.qinghudaixifan;
                tData.areaSelectMode["piaohudaixibufan"] = this.createParams.piaohudaixibufan;
            }
            tData.areaSelectMode["tiehu"] = this.createParams.tiehu;
            tData.areaSelectMode["qitie"] = this.createParams.qitie;
            tData.areaSelectMode["tiehuCount"] = this.createParams.tiehuCount;
            tData.maxPlayer = 3;
            tData.gameCnName = "如东双将";
        }
        else if (tData.gameType == GAME_TYPE.DA_TONG_ZI_SHAO_YANG) { // 邵阳打筒子
            this.pGameCode = gameInstance.pGameCodeDaTongZi;
            tData.deckNum = tData.areaSelectMode["deckNum"] = this.createParams.deckNum; // 牌副数 3,4
            tData.isSiXi = tData.areaSelectMode["isSiXi"] = this.createParams.isSiXi; // 是否天天四喜玩法(4副牌)
            if (tData.deckNum == 3) {
                tData.isSiXi = tData.areaSelectMode["isSiXi"] = false;
            }
            tData.deckNum = tData.areaSelectMode["deckNum"] = this.createParams.deckNum; // 牌副数 3,4
            tData.areaSelectMode.scoreLine = this.createParams.scoreLine; // 分数线
            tData.areaSelectMode.lastRoundScore = this.createParams.lastRoundScore; // 终局奖励分
            tData.areaSelectMode.isAnCards = this.createParams.isAnCards; // 是否按8张牌(4人)
            tData.areaSelectMode.isReCall = this.createParams.isReCall;  // 是否有记牌器(4人)
            tData.areaSelectMode.isShowLeft = this.createParams.isShowLeft; // 是否显示剩余牌数（3,2人)
            tData.areaSelectMode.isRandom = this.createParams.isRandom; // 是否随机出头(3,2人)
            tData.areaSelectMode.haveKingTz = this.createParams.haveKingTz; // 是否有王筒子(3,2人)
            if (tData.isSiXi) {
                tData.areaSelectMode.haveKingTz = false;
            }
            tData.areaSelectMode.isBiChu = this.createParams.isBiChu; // 是否能管必管
            tData.areaSelectMode.isTrust = this.createParams.isTrust || false; // 是否自动托管
            if (tData.maxPlayer == 3) {
                tData.areaSelectMode.isBiChu = true;
            }
            // if (!tData.areaSelectMode.isBiChu) {
            //     tData.areaSelectMode.isTrust = false;
            // }
            tData.areaSelectMode.hasWings = tData.deckNum == 3;
            if (this.createParams.hasWings != undefined) {
                tData.areaSelectMode.hasWings = this.createParams.hasWings; // 是否可带牌
            }
            tData.hasWings = tData.areaSelectMode.hasWings;
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            this.pGameCode.initHandCount(this); // 初始化tData 手牌数等字段
            tData.gameCnName = "邵阳打筒子";
        }
        else if (tData.gameType == GAME_TYPE.RU_GAO_ER) {
            logger.debug("=====GameCode=====runStartGame===========如皋 二人=====================");
            this.pGameCode = gameInstance.pGameCodeRugaoER;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["menzigun"] = this.createParams.menzigun;
            tData.areaSelectMode["shuanglonghui"] = this.createParams.shuanglonghui;
            tData.areaSelectMode["huimianchuhan"] = this.createParams.huimianchuhan;
            tData.areaSelectMode["haidilaoyue"] = this.createParams.haidilaoyue;
            tData.areaSelectMode["xijiang"] = this.createParams.xijiang;
            tData.areaSelectMode["fengding"] = this.createParams.fengding;
            tData.areaSelectMode["daixi"] = this.createParams.daixi;
            tData.areaSelectMode["shengyihu"] = this.createParams.shengyihu;
            tData.areaSelectMode["jizi"] = this.createParams.jizi;
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang;
            tData.areaSelectMode["qinghu"] = this.createParams.qinghu;
            tData.areaSelectMode["piaohu"] = this.createParams.piaohu;
            tData.areaSelectMode["tahu"] = this.createParams.tahu;
            tData.areaSelectMode["tahu80"] = this.createParams.tahu80;
            tData.areaSelectMode["tahuUnlimited"] = this.createParams.tahuUnlimited;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;              // 翻倍类型：0  不翻倍   1 低于xx分翻2倍     2   低于xx分翻3倍
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;    // 翻倍的分数下限
            tData.maxPlayer = 2;
            tData.gameCnName = "二人长牌";
            if (!this.createParams.tahu && !this.createParams.tahu80 && !this.createParams.tahuUnlimited) {
                tData.areaSelectMode["shengyihu"] = false;
            }
        }
        else if (tData.gameType == GAME_TYPE.SHAO_YANG_ZI_PAI) { // 邵阳字牌
            this.pGameCode = gameInstance.pGameCodePaoHuZiShaoYangZiPai;
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime;
            }
            this.pGameCode.initTable(this); // 初始化tData
            tData.areaSelectMode["huXiType"] = this.createParams.huXiType || 5; // 3胡息一屯或者5胡息一屯
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui || false; // 是否加锤
            tData.areaSelectMode["hongHeiType"] = this.createParams.hongHeiType || 0; //红黑胡翻倍/+10囤
            tData.areaSelectMode["isZiMo"] = this.createParams.isZiMo || false;       //是否自摸+1囤
            tData.maxPlayer = this.createParams.maxPlayer;
            tData.minHuxi = 10;
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode.diFen = this.createParams.diFen || -1; // 低分翻倍 -1 不翻倍 5-10...-100 x分
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            if (tData.maxPlayer == 2 || tData.areaSelectMode.convertible) {
                tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            } else {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            tData.gameCnName = "邵阳字牌";
        }
        else if (tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI) { // 邵阳剥皮
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if (this.createParams.fieldCountdown > 0) {
                tData.areaSelectMode.trustTime = this.createParams.fieldCountdown + 1;
            }
            // this.pGameCode = gameInstance.pGameCodeShaoYangZiPai;
            this.pGameCode = gameInstance.pGameCodePaoHuZiBoPi;
            if (tData.areaSelectMode.trustTime != -1) { // 剥皮托管重构流程(金币场)
                // this.pGameCode = gameInstance.pGameCodePaoHuZiBoPi;
                this.pGameCode.initTable(this); // 初始化tData
            }
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            if (this.createParams.tianDiHuType == undefined) {
                if (this.createParams.isTianDiHu == undefined) {
                    tData.areaSelectMode.tianDiHuType = 0;
                }
                else {
                    tData.areaSelectMode.tianDiHuType = this.createParams.isTianDiHu ? 0 : 2;
                }
            }
            else {
                tData.areaSelectMode.tianDiHuType = this.createParams.tianDiHuType; // 天地胡类型 0:+10 1:翻倍 2:无
            }

            tData.areaSelectMode.isSpringDouble = this.createParams.isSpringDouble || false; // 是否春天翻倍
            tData.areaSelectMode.isDismissDouble = this.createParams.isDismissDouble; // 是否春天解散翻倍
            tData.areaSelectMode["isLianZhuang"] = this.createParams.isLianZhuang; // 是否连庄
            tData.areaSelectMode["fengDing"] = this.createParams.fengDing; //封顶
            tData.areaSelectMode["zuoXing"] = this.createParams.zuoXing || false;//坐醒
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui || false; // 是否加锤
            tData.areaSelectMode["isRandomZhuang"] = this.createParams.isRandomZhuang || false; // 随机坐庄
            tData.areaSelectMode["isMaiPai"] = this.createParams.isMaiPai; // 是否埋牌
            if (!this.createParams.isMaiPai) {
                this.createParams.maiPaiNum = 0;
            }
            tData.areaSelectMode["maiPaiNum"] = this.createParams.maiPaiNum; // 埋牌张数
            tData.areaSelectMode["isHongheidian"] = this.createParams.isHongheidian; // 是否有红黑点
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            if (this.createParams.hongHeiType == undefined) {
                if (this.createParams.isHongheidian) {
                    if (tData.maxPlayer == 4 && !tData.areaSelectMode.zuoXing) {
                        tData.areaSelectMode.hongHeiType = 1;
                    }
                    else {
                        tData.areaSelectMode.hongHeiType = 2;
                    }
                }
                else {
                    tData.areaSelectMode.hongHeiType = 0;
                }
            }
            else {
                tData.areaSelectMode.hongHeiType = this.createParams.hongHeiType; // 红黑类型 0:无 1:红黑胡 2:红黑点
            }
            tData.areaSelectMode.diFen = this.createParams.diFen || -1; // 低分翻倍 -1 不翻倍 5-10...-100 x分
            tData.minHuxi = 10;
            if (tData.maxPlayer == 4 && !tData.areaSelectMode.zuoXing) {
                tData.minHuxi = 6;
            }
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分

            tData.areaSelectMode.trustWay = this.createParams.trustWay;//托管方式
            if (tData.areaSelectMode.isTrustWhole && tData.areaSelectMode.trustWay == 0) {
                tData.areaSelectMode.trustWay = 2;
            }
            tData.gameCnName = "邵阳剥皮";
        }
        else if (tData.gameType == GAME_TYPE.DA_ZI_BO_PI) { // 邵阳剥皮
            this.pGameCode = gameInstance.pGameCodePaoHuZiDaZiBoPi;
            tData.gameCnName = "大字剥皮";
        }
        else if (tData.gameType == GAME_TYPE.DANG_YANG_FAN_JING) { // 当阳翻精
            this.pGameCode = gameInstance.pGameCodeDangYangFanJing;
        }
        else if (tData.gameType == GAME_TYPE.YI_CHANG_SHANG_DA_REN) { // 宜昌上大人
            this.pGameCode = gameInstance.pGameCodeYiChangShangDaRen;
        }
        else if (tData.gameType == GAME_TYPE.WANG_DIAO_MA_JIANG) { // 王钓麻将
            this.pGameCode = gameInstance.pGameCodeWangDiaoMJ;
            tData.areaSelectMode["isLiangPian"] = this.createParams.isLiangPian; // 两片
            tData.areaSelectMode["wuTong"] = "wuTong" in this.createParams ? this.createParams.wuTong : false; // 无筒
            tData.areaSelectMode["isQiangGangHu"] = this.createParams.isQiangGangHu; // 抢杠胡
            tData.areaSelectMode["maPai"] = this.createParams.maPai; //码牌
            tData.areaSelectMode["maFen"] = this.createParams.maFen || 1;//码分
            tData.areaSelectMode["qidui"] = this.createParams.qidui || false;
            tData.areaSelectMode["zhuangxianfen"] = this.createParams.zhuangxianfen || false;//码分
            tData.areaSelectMode["pengpenghu"] = this.createParams.pengpenghu || false;//碰碰胡
            tData.areaSelectMode["isErfen"] = this.createParams.isErfen || false;//底分2分
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
            tData.areaSelectMode["trustTime"] = "trustTime" in this.createParams ? this.createParams.trustTime : 0;
            tData.areaSelectMode.fengDing = "fengDing" in this.createParams ? this.createParams.fengDing : 0;
            tData.areaSelectMode.quanjufengdingScore = "quanjufengdingScore" in this.createParams ? this.createParams.quanjufengdingScore : 0;
            tData.areaSelectMode.trustWay = "trustWay" in this.createParams ? this.createParams.trustWay : 0;
            tData.areaSelectMode.piaoFen = "piaoFen" in this.createParams ? this.createParams.piaoFen : false;
            tData.areaSelectMode.quanKaiFang = "quanKaiFang" in this.createParams ? this.createParams.quanKaiFang : false;
            tData.gameCnName = "王钓麻将";
        }
        else if (tData.gameType == GAME_TYPE.JIN_ZHONG_LI_SI) {
            logger.debug("=====GameCode=====runStartGame===========晋中立四麻将=====================");
            this.pGameCode = gameInstance.pGameCodeJinZhongLiSi;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["guaisanjiao"] = this.createParams.guaisanjiao;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["gangsuihuzou"] = this.createParams.gangsuihuzou;
            tData.areaSelectMode["guohuzhinenglongpai"] = this.createParams.guohuzhinenglongpai;
            tData.areaSelectMode["passhudapai"] = this.createParams.passhudapai;
            tData.gameCnName = "晋中立四麻将";
        }
        else if (tData.gameType == GAME_TYPE.XIANG_XIANG_GAO_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========湘乡告胡子=====================");
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }

            this.pGameCode = gameInstance.pGameCodeXiangXiangZiPai;
            //对应湘乡告胡子玩法规则模块
            tData.areaSelectMode["tuototuo"] = this.createParams.tuototuo;  // 0:不打坨， 1：坨对坨4番 2：坨对坨3番
            tData.minHuxi = 15;
            tData.gameCnName = "湘乡告胡子";
            tData.areaSelectMode.zuoZhuang = "zuoZhuang" in this.createParams ? this.createParams.zuoZhuang : 1;//0：随机 1：房主
            tData.areaSelectMode.maipai = "maipai" in this.createParams ? this.createParams.maipai : true;
            tData.areaSelectMode.maiPaiType = "maiPaiType" in this.createParams ? this.createParams.maiPaiType : 0;//埋牌类型  0：不埋牌   1：埋10    2：埋20
            if (!tData.areaSelectMode.maipai) tData.areaSelectMode.maiPaiType = 0;
            if (tData.areaSelectMode.maipai && tData.areaSelectMode.maiPaiType == 0) tData.areaSelectMode.maiPaiType = 2;
            tData.areaSelectMode.fullperson = "fullperson" in this.createParams ? this.createParams.fullperson : false;  //勾选满人为true，否则为false
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["faPai"] = "faPai" in this.createParams ? this.createParams.faPai : 0;
        }
        else if (tData.gameType == GAME_TYPE.LOU_DI_FANG_PAO_FA) {
            logger.debug("=====GameCode=====runStartGame===========娄底放炮罚=====================");
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }

            this.pGameCode = gameInstance.pGameCodeXiangXiangZiPai;
            //tData.areaSelectMode["limitjushu"] = this.createParams.limitjushu;  //true：不限局数， false：限制局数
            tData.areaSelectMode["isfending"] = this.createParams.isfending;   //封顶200 true， 封顶400  flase
            tData.areaSelectMode["tuototuo"] = this.createParams.tuototuo;  // 0:不打坨，  2：坨对坨(胡息打鸟)  1:分数打鸟
            tData.areaSelectMode["isjiepao"] = this.createParams.isjiepao;  // true: 接炮100封顶， false：100胡息
            tData.areaSelectMode["gametype"] = this.createParams.gametype;   //1：湘乡娄底放炮罚 2：邵阳娄底
            tData.areaSelectMode["zuoZhuang"] = "zuoZhuang" in this.createParams ? this.createParams.zuoZhuang : 1;  //首局坐庄   1：房主庄   0：随机庄
            tData.areaSelectMode.maipai = "maipai" in this.createParams ? this.createParams.maipai : true;
            tData.minHuxi = 15;
            tData.areaSelectMode.fullperson = "fullperson" in this.createParams ? this.createParams.fullperson : false;  //勾选满人为true，否则为false
            tData.areaSelectMode.piaohu = "piaohu" in this.createParams ? this.createParams.piaohu : false;  //勾选飘胡为true，否则为false
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.gameCnName = "娄底放炮罚";
        }
        else if (tData.gameType == GAME_TYPE.SHAO_YANG_FANG_PAO_FA) {
            logger.debug("=====GameCode=====runStartGame===========邵阳放炮罚=====================");
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if (this.createParams.fieldCountdown > 0) {
                tData.areaSelectMode.trustTime = this.createParams.fieldCountdown + 1;
            }
            this.pGameCode = gameInstance.pGameCodeShaoYangFangPaoFa;
            if (tData.areaSelectMode.trustTime != -1 && !this.createParams.normalFangPaoFa) { // 放炮罚金币场
                this.pGameCode = gameInstance.pGameCodeFieldFangPaoFa;
                this.pGameCode.initTable(this); // 初始化tData
            }
            //对应娄底放炮罚的玩法规则模块
            //tData.areaSelectMode["limitjushu"] = this.createParams.limitjushu;  //true：不限局数， false：限制局数
            tData.areaSelectMode["isfending"] = this.createParams.isfending;   //封顶200 true， 封顶400  flase
            tData.areaSelectMode["tuototuo"] = this.createParams.tuototuo;  // 0:不打坨，  2：坨对坨(胡息打鸟)  1:分数打鸟
            tData.areaSelectMode["isjiepao"] = this.createParams.isjiepao;  // true: 接炮100封顶， false：100胡息
            tData.areaSelectMode["gametype"] = this.createParams.gametype;   //1：湘乡娄底放炮罚 2：邵阳娄底
            tData.areaSelectMode["faPai"] = "faPai" in this.createParams ? this.createParams.faPai : 1;
            tData.areaSelectMode["fenshutuo"] = "fenshutuo" in this.createParams ? this.createParams.fenshutuo : 10;
            tData.areaSelectMode["zuoXing"] = "zuoXing" in this.createParams ? this.createParams.zuoXing : false;
            tData.areaSelectMode.isMaiPai = "isMaiPai" in this.createParams ? this.createParams.isMaiPai : true;  //勾选为true，否则为false
            tData.areaSelectMode.maiPaiType = "maiPaiType" in this.createParams ? this.createParams.maiPaiType : 0; //埋牌类型
            if (tData.areaSelectMode.isMaiPai && tData.areaSelectMode.maiPaiType == 0) tData.areaSelectMode.maiPaiType = 2;
            tData.areaSelectMode.fanBei = "fanBei" in this.createParams ? this.createParams.fanBei : false; //  0：不翻倍1：≤50分翻倍2： ≤100分翻倍3≤150分翻倍4≤200分翻倍5：不限分翻倍
            tData.areaSelectMode.zuoZhuang = "zuoZhuang" in this.createParams ? this.createParams.zuoZhuang : 1; //  0：随机 1：房主
            tData.areaSelectMode["minHuType"] = "minHuType" in this.createParams ? this.createParams.minHuType : 0; // 胡法 0:15胡起胡 1:10胡起胡
            tData.areaSelectMode["keSiShou"] = "keSiShou" in this.createParams ? this.createParams.keSiShou : false; // 可死守 0:15胡起胡 1:10胡起胡
            tData.minHuxi = tData.areaSelectMode.minHuType == 1 ? 10 : 15;
            tData.areaSelectMode.fullperson = "fullperson" in this.createParams ? this.createParams.fullperson : false;  //勾选满人为true，否则为false
            tData.areaSelectMode.piaohu = "piaohu" in this.createParams ? this.createParams.piaohu : false;  //勾选飘胡为true，否则为false
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["tuoMult"] = "tuoMult" in this.createParams ? this.createParams.tuoMult : 4;// 打坨
            tData.gameCnName = "娄底放炮罚";
        }
        else if (tData.gameType == GAME_TYPE.HENG_YANG_FANG_PAO_FA) {
            logger.debug("=====GameCode=====runStartGame===========衡阳放炮罚=====================");
            this.pGameCode = gameInstance.pGameCodeHYFangPaoFa;
            //对应娄底放炮罚的玩法规则模块
            //tData.areaSelectMode["limitjushu"] = this.createParams.limitjushu;  //true：不限局数， false：限制局数
            tData.areaSelectMode["isfending"] = this.createParams.isfending;   //封顶200 true， 封顶400  flase
            tData.areaSelectMode["tuototuo"] = this.createParams.tuototuo;  // 0:不打坨，  2：坨对坨(胡息打鸟)  1:分数打鸟
            tData.areaSelectMode["isjiepao"] = this.createParams.isjiepao;  // true: 接炮100封顶， false：100胡息
            tData.areaSelectMode["gametype"] = this.createParams.gametype;   //1：湘乡娄底放炮罚 2：邵阳娄底
            tData.areaSelectMode["fenshutuo"] = "fenshutuo" in this.createParams ? this.createParams.fenshutuo : 10;
            tData.areaSelectMode["zuoXing"] = "zuoXing" in this.createParams ? this.createParams.zuoXing : false;
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode.diFen = "diFen" in this.createParams ? this.createParams.diFen : -1; // 分低于多少翻倍(-1.不翻 10.10分 20.20分 30.30分 10000:不限分)
            tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            if (tData.maxPlayer == 2) {
                tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;  //埋牌
            } else {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            tData.minHuxi = 15;
            tData.gameCnName = "衡阳放炮罚";
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_LIN_FEN) {
            logger.debug("=====GameCode=====runStartGame===========临汾斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuLF;
        }
        else if (tData.gameType == GAME_TYPE.LIN_FEN_YING_SAN_ZUI) {
            logger.debug("=====GameCode=====runStartGame===========临汾硬三嘴麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLinfenYingSanZuiMJ;
            tData.areaSelectMode["gangsui"] = this.createParams.gangsui;
            tData.areaSelectMode["angangkejian"] = this.createParams.angangkejian;
            tData.areaSelectMode["zhuangfen2"] = this.createParams.zhuangfen2;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.gameCnName = "临汾硬三嘴麻将";
        }
        else if (tData.gameType == GAME_TYPE.LIN_FEN_YI_MEN_ZI) {
            logger.debug("=====GameCode=====runStartGame===========临汾一门子麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLinfenYiMenZiMJ;
            tData.areaSelectMode["gangsui"] = this.createParams.gangsui;
            tData.areaSelectMode["zhuangfen1"] = this.createParams.zhuangfen1;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["shuye"] = this.createParams.shuye;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.gameCnName = "临汾一门子麻将";
        }
        else if (tData.gameType == GAME_TYPE.SHAO_YANG_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========邵阳麻将=====================");
            this.pGameCode = gameInstance.pGameCodeShaoYangMJ;
            tData.canHu7 = true;
            tData.isHunCardPlay = false;
            tData.areaSelectMode["isDaiFeng"] = this.createParams.isDaiFeng;       //带风
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui;       //加锤
            tData.areaSelectMode["isCanChi"] = this.createParams.isCanChi;         //可以吃
            tData.areaSelectMode["isQingYiSeChi"] = this.createParams.isQingYiSeChi;//清一色可吃
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;         //抓鸟
            tData.areaSelectMode["syDuoHu"] = "syDuoHu" in this.createParams ? this.createParams.syDuoHu : true; // 一炮多响
            // 新增
            tData.areaSelectMode["isLianChui"] = this.createParams.isLianChui || false;     // 是否连锤
            tData.areaSelectMode["fangGangType"] = this.createParams.fangGangType || 0;     // 放杠出分类型
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["trustWay"] = "trustWay" in this.createParams ? this.createParams.trustWay : 0;// 托管方式
            tData.gameCnName = "邵阳麻将";
        }
        else if (tData.gameType == GAME_TYPE.FEN_XI_YING_KOU) {
            logger.debug("=====GameCode=====runStartGame===========临汾硬扣麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLinfenYingKou;
            tData.areaSelectMode["zhuangfen2"] = this.createParams.zhuangfen2;
            tData.areaSelectMode["gangsuihu"] = this.createParams.gangsuihu;
            tData.areaSelectMode["isDaiFeng"] = this.createParams.isDaiFeng;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["angangbukejian"] = this.createParams.angangbukejian;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.gameCnName = "临汾硬扣麻将";
        }
        else if (tData.gameType == GAME_TYPE.JI_XIAN_1928_JIA_ZHANG) {
            logger.debug("=====GameCode=====runStartGame===========临汾吉县1928夹张麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLinfenJiXian1928JiaZhangMJ;
            tData.areaSelectMode["special"] = this.createParams.special;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["jiazhang"] = this.createParams.jiazhang;
            tData.areaSelectMode["sanmen"] = this.createParams.sanmen;
            tData.areaSelectMode["gangsui"] = this.createParams.gangsui;
            tData.areaSelectMode["lunzhuang"] = true;
            tData.gameCnName = "临汾吉县1928夹张麻将";
        }
        else if (tData.gameType == GAME_TYPE.ML_HONG_ZI) { // 汨罗红字
            this.pGameCode = gameInstance.pGameCodeMLZP;
            tData.minRedNum = tData.areaSelectMode.minRedNum = this.createParams.minRedNum; // 起胡红数(3, 4)
            tData.areaSelectMode.kingNum = this.createParams.kingNum; // 王数(0-4)
            tData.areaSelectMode.isShuangHe = this.createParams.isShuangHe; // 双合翻倍
            tData.areaSelectMode.isPengPengHu = this.createParams.isPengPengHu == undefined ? true : this.createParams.isPengPengHu; // 是否有碰碰胡
            tData.areaSelectMode.isYiGuaBian = this.createParams.isYiGuaBian == undefined ? true : this.createParams.isYiGuaBian; // 是否一挂匾
            tData.areaSelectMode.isManTangHong = this.createParams.isManTangHong == undefined ? true : this.createParams.isManTangHong; // 是否有满堂红
            tData.areaSelectMode.isHuDieFei = this.createParams.isHuDieFei == undefined ? true : this.createParams.isHuDieFei; // 是否有蝴蝶飞
            tData.areaSelectMode.isSiPeng = this.createParams.isSiPeng || false; // 是否有四碰单吊
            tData.areaSelectMode.isDaHu = this.createParams.isDaHu || false; // 是否大胡十分
            tData.areaSelectMode.isBanBanHu = this.createParams.isBanBanHu == undefined ? true : this.createParams.isBanBanHu; // 是否板板胡
            tData.areaSelectMode.isJuJuHong = this.createParams.isJuJuHong == undefined ? true : this.createParams.isJuJuHong; // 是否有句句红
            tData.areaSelectMode.isShiErHong = this.createParams.isShiErHong || false; // 是否有十二红
            tData.areaSelectMode.isShiYiHong = this.createParams.isShiYiHong || false; // 是否有十一红
            tData.areaSelectMode.isFengDing = this.createParams.isFengDing || false; // 是否封顶80分
            //tData.areaSelectMode.isMaiPai = tData.maxPlayer == 2 && "isMaiPai" in this.createParams ? this.createParams.isMaiPai : false;
            tData.areaSelectMode.isMaiPai = this.createParams.isMaiPai;
            // 二人玩法埋牌
            tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            if (!tData.areaSelectMode.isMaiPai) {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            if (tData.areaSelectMode.isBanBanHu) { // 板板胡类型 (0 胡闲家摸的第一张牌  1.胡自己摸的第一张牌)
                tData.areaSelectMode.banBanHuType = this.createParams.banBanHuType == undefined ? 0 : this.createParams.banBanHuType;
            }
            tData.areaSelectMode.isManualCutCard = this.createParams.isManualCutCard || false;//true/false手动/系统切牌
            tData.areaSelectMode.fanBei = this.createParams.fanBei;//0 不翻倍 1 翻倍
            tData.areaSelectMode.fanBeiScore = this.createParams.fanBeiScore;// 低于多少翻倍
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
            tData.areaSelectMode.isRandomZhuang = this.createParams.isRandomZhuang;   // 随机坐庄
            tData.areaSelectMode.isHuaHu = this.createParams.isHuaHu; // 是否有花胡
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime;
                this.pGameCode.initTable(this);
            }
            tData.areaSelectMode.trustWay = this.createParams.trustWay || 0;//托管方式
            tData.gameCnName = "汨罗红字";
        }
        else if (tData.gameType == GAME_TYPE.XIANG_YIN_ZHUO_HONG_ZI) { // 湘阴捉红字
            this.pGameCode = gameInstance.pGameCodeXYHZ;
            tData.minRedNum = tData.areaSelectMode.minRedNum = this.createParams.minRedNum; // 起胡红数(3, 4)
            tData.areaSelectMode.kingNum = this.createParams.kingNum; // 王数(0-4)
            tData.areaSelectMode.isSiPeng = this.createParams.isSiPeng; // 是否有四碰单吊
            tData.areaSelectMode.isJuJuHong = this.createParams.isJuJuHong;       // 是否有句句红// 名字要更换成花胡
            tData.areaSelectMode.isPengPengHu = this.createParams.isPengPengHu;    // 是否有碰碰胡 (文档没写)
            tData.areaSelectMode.isManTangHong = this.createParams.isManTangHong;   // 是否有满堂红 (文档没写)
            //tData.areaSelectMode.isMaiPai = tData.maxPlayer == 2 && "isMaiPai" in this.createParams ? this.createParams.isMaiPai : false;          // 二人玩法埋牌
            tData.areaSelectMode.isShuangHe = true;       // 双合翻倍 固定选项
            tData.areaSelectMode.isFengDing = true;       // 是否封顶80分 固定选项
            tData.areaSelectMode.isYiGuaBian = false;     // 是否一挂匾   固定选项
            tData.areaSelectMode.isHuDieFei = false;      // 是否有蝴蝶飞 固定选项
            tData.areaSelectMode.isDaHu = false;          // 是否大胡十分 固定选项
            tData.areaSelectMode.isBanBanHu = false;      // 是否板板胡   固定选项
            tData.areaSelectMode.isShiErHong = false;     // 是否有十二红 固定选项
            tData.areaSelectMode.isShiYiHong = false;     // 是否有十一红 固定选项
            tData.areaSelectMode["isQiDui"] = "isQiDui" in this.createParams ? this.createParams.isQiDui : true;//七对，默认勾选

            //埋牌相关
            tData.areaSelectMode.isMaiPai = this.createParams.isMaiPai;
            // 二人玩法埋牌
            tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            if (!tData.areaSelectMode.isMaiPai) {
                tData.areaSelectMode.maiPaiNum = 0;
            }

            tData.areaSelectMode["isShangShou"] = "isShangShou" in this.createParams ? this.createParams.isShangShou : false;//王是否可以上手
            // if (tData.areaSelectMode.isBanBanHu) { // 板板胡类型 (0 胡闲家摸的第一张牌  1.胡自己摸的第一张牌)
            //     tData.areaSelectMode.banBanHuType = this.createParams.banBanHuType == undefined ? 0 : this.createParams.banBanHuType;
            // }
            tData.areaSelectMode.isManualCutCard = this.createParams.isManualCutCard || false;   //切牌 true/false
            tData.areaSelectMode.fanBei = this.createParams.fanBei;//0 不翻倍 1 翻倍
            tData.areaSelectMode.fanBeiScore = this.createParams.fanBeiScore;// 低于多少翻倍
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;//结算底分
            tData.areaSelectMode.isRandomZhuang = this.createParams.isRandomZhuang;   // 随机坐庄
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime;
                this.pGameCode.initTable(this);
            }
            tData.gameCnName = "湘阴捉红字";
        }
        else if (tData.gameType == GAME_TYPE.LIN_FEN_XIANG_NING_SHUAI_JIN) {
            logger.debug("=====GameCode=====runStartGame===========临汾乡宁摔金=====================");
            this.pGameCode = gameInstance.pGameCodeMJLFXNSJ;
        }
        else if (tData.gameType == GAME_TYPE.KOU_DIAN_FENG_ZUI_ZI) {
            logger.debug("=====GameCode=====runStartGame===========临汾扣点风嘴子=====================");
            this.pGameCode = gameInstance.pGameCodeLinFenKouDianFengZuiZiMJ;
            tData.areaSelectMode["lunzhuang"] = true;
            // tData.areaSelectMode["flowerType"] = this.createParams.flowerType;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，2 风耗子，3随机耗子
            tData.areaSelectMode["gangsui"] = this.createParams.gangsui;
            tData.areaSelectMode["dianpao"] = this.createParams.dianpao;
            tData.areaSelectMode["bihu"] = this.createParams.bihu;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "临汾扣点风嘴子";
        }
        else if (tData.gameType == GAME_TYPE.XIAO_YI_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========吕梁孝义扣点=====================");
            this.pGameCode = gameInstance.pGameCodeLvliangXiaoyiKoudian;
        }
        else if (tData.gameType == GAME_TYPE.HENG_YANG_SHIWUHUXI) { // 衡阳15胡
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (('trustTime' in this.createParams) && this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }

            this.pGameCode = gameInstance.pGameCodeHY_15HX_ZP;
            if (tData.areaSelectMode.trustTime != -1) { // 托管重构流程
                this.pGameCode = gameInstance.pGameCodeShiWuHu;
                this.pGameCode.initTable(this); // 初始化tData
            }
            tData.areaSelectMode["bihuType"] = this.createParams.bihuType; // (0.有胡必胡 1.点炮必胡 2. 无)
            tData.areaSelectMode["xingType"] = this.createParams.xingType; // (0不带省 1跟省 2翻省)
            tData.areaSelectMode["isErfen"] = this.createParams.isErfen; // 底分2分 true； 否则 false
            tData.areaSelectMode["isZiMoRate"] = this.createParams.isZiMoRate; // 自摸翻倍： true；否则 false
            tData.areaSelectMode["isHongheidian"] = this.createParams.isHongheidian; //红黑点 true， 否则 false
            tData.areaSelectMode["isAlldemit"] = this.createParams.isAlldemit == undefined ? 0 : this.createParams.isAlldemit;//0 不选择 1选中
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode.diFen = "diFen" in this.createParams ? this.createParams.diFen : -1; // 分低于多少翻倍(-1.不翻 10.10分 20.20分 30.30分)
            tData.areaSelectMode["isYiwushi"] = this.createParams.isYiwushi; //一五十
            tData.minHuxi = 15;
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            tData.gameCnName = "衡阳十五胡";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_HUA_HONG_GUAI_WAN) {
            logger.debug("=====GameCode=====runStartGame===========怀化红拐弯=====================");
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            this.pGameCode = gameInstance.pGameCodeHuaiHuaRedGuaiWan;
            //对应怀化红拐弯玩法规则模块
            tData.minHuxi = 15;
            tData.gameCnName = "怀化红拐弯";
            tData.areaSelectMode["shiwuzimo"] = "shiwuzimo" in this.createParams ? this.createParams.shiwuzimo : false; // true: 自摸 否则 false
            tData.areaSelectMode["redguaiwanOne"] = this.createParams.redguaiwanOne;  //1： 红拐弯 234； 2:红拐弯468
            tData.areaSelectMode["redguaiwanwanfa"] = this.createParams.redguaiwanwanfa; //红拐弯玩法一次2^n （1:天胡，2:地胡，4:点胡, 8:红胡, 16:乌胡， 碰碰胡:32, 64:十八大, 128:十六小, 256:海底胡// ）
            tData.areaSelectMode["iszimo"] = this.createParams.iszimo;  // 0 不自摸 1 自摸
            tData.areaSelectMode["huangzhuang"] = "huangzhuang" in this.createParams ? this.createParams.huangzhuang : false;
            tData.areaSelectMode["datuanyuan"] = "datuanyuan" in this.createParams ? this.createParams.datuanyuan : false;
            tData.areaSelectMode["shuahou"] = "shuahou" in this.createParams ? this.createParams.shuahou : false;
            tData.areaSelectMode["maipai"] = "maipai" in this.createParams ? this.createParams.maipai : false;// 2人玩法 true ：埋牌 否则 false
            tData.areaSelectMode["maiPaiType"] = "maiPaiType" in this.createParams ? this.createParams.maiPaiType : 0;//埋牌类型  0:不埋牌   1:埋10张  2:埋20张
            if (!tData.areaSelectMode.maipai) tData.areaSelectMode.maiPaiType = 0;
            if (tData.areaSelectMode.maipai && tData.areaSelectMode.maiPaiType == 0) tData.areaSelectMode.maiPaiType = 2;
            tData.areaSelectMode["isManualCutCard"] = "isManualCutCard" in this.createParams ? this.createParams.isManualCutCard : false;
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["faPai"] = "faPai" in this.createParams ? this.createParams.faPai : 0;// 发牌速度
        }
        else if (tData.gameType == GAME_TYPE.XU_ZHOU_PEI_XIAN) {
            logger.debug("=====GameCode=====runStartGame===========徐州沛县麻将=====================");
            this.pGameCode = gameInstance.pGameCodeXuzhouPeiXianMJ;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1; // 底分
            tData.areaSelectMode["canChi"] = this.createParams.canChi; // 是否可吃
            tData.areaSelectMode["isFenCard"] = this.createParams.isFenCard; // 是否有风牌：东南西北
            tData.areaSelectMode["isQingYiSe"] = this.createParams.isQingYiSe; // 混一色:false & 清一色:true
            tData.areaSelectMode["isQiDui"] = this.createParams.isQiDui; // 七对
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu; // 一炮多响
            tData.areaSelectMode["maizhuang"] = this.createParams.maizhuang; // 是否可下码
            tData.gameCnName = "徐州沛县麻将";
        }
        else if (tData.gameType == GAME_TYPE.LV_LIANG_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========吕梁麻将=====================");
            this.pGameCode = gameInstance.pGameCodeLvliangMJ;
            tData.areaSelectMode["qingLongDouble"] = this.createParams.qingLongDouble;
            //tData.areaSelectMode["guozi"] = this.createParams.guozi;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["biHu"] = this.createParams.biHu;
            tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，1 捉耗子 2 双耗子
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["mingGangZiMo"] = this.createParams.mingGangZiMo;//明杠自摸经典和单耗子
            tData.areaSelectMode["mingGangZiMoShuang"] = this.createParams.mingGangZiMoShuang;//明杠自摸双耗子
            tData.areaSelectMode["jiaZhuangFen"] = this.createParams.jiaZhuangFen;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["zimo12"] = this.createParams.zimo12;
            tData.gameCnName = "吕梁麻将";
        }
        else if (tData.gameType == GAME_TYPE.LV_LIANG_DA_QI) {
            logger.debug("=====GameCode=====runStartGame===========吕梁打七=====================");
            this.pGameCode = gameInstance.pGameCodePokerLvliangDaqi;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_LV_LIANG) {
            logger.debug("=====GameCode=====runStartGame===========吕梁斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuLL;
        }
        else if (tData.gameType == GAME_TYPE.XIN_NING_MA_JIANG) {
            this.pGameCode = gameInstance.pGameCodeXinNingMJ;
            tData.areaSelectMode["isCanChi"] = this.createParams.isCanChi;         //可以吃
            tData.areaSelectMode["isDaiFeng"] = this.createParams.isDaiFeng;       //带风
            tData.areaSelectMode["zhiTouZi"] = this.createParams.zhiTouZi;           //开杠听掷骰子
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui;       //加锤
            tData.areaSelectMode["isLianChui"] = this.createParams.isLianChui;     //是否连锤
            tData.areaSelectMode["huType"] = this.createParams.huType;             //胡牌类型
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;         //抓鸟
            tData.areaSelectMode["fangGangType"] = this.createParams.fangGangType;     // 放杠出分类型

            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            // 庄上锤
            tData.areaSelectMode["isZhuangShangChui"] = "isZhuangShangChui" in this.createParams ? this.createParams.isZhuangShangChui : false;

            tData.gameCnName = "新宁麻将";
        }
        else if (tData.gameType == GAME_TYPE.FAN_SHI_XIA_YU) {
            logger.debug("=====GameCode=====runStartGame===========繁峙下雨麻将=====================");
            this.pGameCode = gameInstance.pGameCodeFanShiXiaYu;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;          //轮庄
            tData.areaSelectMode["dianpao"] = this.createParams.dianpao;              //点炮包胡
            tData.areaSelectMode["type"] = this.createParams.type;                    //0大胡,1小胡
            tData.areaSelectMode["zhuangjiajia1"] = this.createParams.zhuangjiajia1;  //庄家加1分
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;             //底分
            tData.areaSelectMode["huangzhuang"] = this.createParams.huangzhuang;      //荒庄不荒杠
            // tData.areaSelectMode["duoHu"] = false;
            // 新加玩法
            tData.areaSelectMode["isTiandihu"] = this.createParams.isTiandihu;        //是否可以天胡地胡
            tData.areaSelectMode["isDasixi"] = this.createParams.isDasixi;            //是否有大四喜牌型
            tData.gameCnName = "繁峙下雨麻将";
        }
        else if (tData.gameType == GAME_TYPE.DAI_XIAN_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========代县麻将=====================");
            this.pGameCode = gameInstance.pGameCodeDaiXianMaJiang;
            tData.areaSelectMode["wanfa"] = this.createParams.wanfa;//0  34自摸， 1  345自摸
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["biHu"] = this.createParams.biHu;
            tData.areaSelectMode["pinghu"] = this.createParams.pinghu;
            tData.areaSelectMode["dankou"] = this.createParams.dankou;
            // tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["dianpaobaogang"] = this.createParams.dianpaobaogang;
            // tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，1 不带风，2 带耗子  3 随机耗子 4 双耗子
            tData.areaSelectMode["jiacheng"] = this.createParams.jiacheng;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "代县麻将";
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_HONG_ZHONG) {
            logger.debug("=====GameCode=====runStartGame===========岳阳红中=====================");
            this.pGameCode = gameInstance.pGameCodeYueYangHongZhong;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;    // 抓鸟数量  1:一码全中   2:抓2个鸟.... 3 4 6
            tData.areaSelectMode["dianpao"] = false; // this.createParams.dianpao;
            tData.areaSelectMode["zhuangxianfen"] = this.createParams.zhuangxianfen;      // 庄闲分
            tData.areaSelectMode["qianggang"] = true;//this.createParams.qianggang;   // 可抢杠胡
            tData.areaSelectMode["qianggangquanbao"] = true;// this.createParams.qianggangquanbao;
            tData.areaSelectMode["hongzhong8"] = this.createParams.hongzhong8;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["zimohu"] = this.createParams.zimohu;
            // tData.areaSelectMode["youhongzhongbujiepao"] = this.createParams.youhongzhongbujiepao;
            tData.areaSelectMode["youhongzhongkeqiangganghu"] = true;// this.createParams.youhongzhongkeqiangganghu;
            tData.areaSelectMode["wuhongzhongjiabei"] = true;   // this.createParams.wuhongzhongjiabei;   // 胡牌手上无红中 加倍
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["hongzhongkehu"] = true; //this.createParams.hongzhongkehu;  // 4红中可起手胡， 8红中玩法不能起手胡
            tData.areaSelectMode["niaofen"] = this.createParams.niaofen || 1;
            tData.areaSelectMode["duoHu"] = true;// this.createParams.duoHu;
            tData.areaSelectMode["qdpphthqysjf"] = this.createParams.qdpphthqysjf;
            tData.gameCnName = "岳阳红中";
        }
        else if (tData.gameType == GAME_TYPE.LONG_HUI_BA_ZHA_DAN) { // 隆回霸炸弹
            this.pGameCode = gameInstance.pGameCodeBaZhaDan;
            tData.deckNum = tData.areaSelectMode["deckNum"] = this.createParams.deckNum; // 牌副数 3,4
            tData.areaSelectMode.scoreLine = this.createParams.scoreLine; // 分数线
            tData.areaSelectMode.isShowLeft = this.createParams.isShowLeft; // 是否显示剩余牌数
            tData.areaSelectMode.hasJoker = this.createParams.hasJoker;   // 是否有带双王
            tData.areaSelectMode.addJoker = this.createParams.addJoker;   // 是否加双王
            tData.areaSelectMode.noJoker = this.createParams.noJoker;   // 是否加双王
            tData.areaSelectMode.jieFengType = this.createParams.jieFengType || 0;   // 接风类型 0 队友 1 下家
            tData.areaSelectMode.isDivideTeam = this.createParams.isDivideTeam;     // 是否分组
            tData.areaSelectMode.isRandomTeam = this.createParams.isRandomTeam || false;     // 是否随机分组
            tData.areaSelectMode.shunType = tData.shunType = 'shunType' in this.createParams ? this.createParams.shunType : 0; // 0顺子到2  1顺子到小王
            tData.areaSelectMode.hasXiFen = 'hasXiFen' in this.createParams ? this.createParams.hasXiFen : false; // 是否有炸弹分
            tData.areaSelectMode.isAnCards = this.createParams.isAnCards;     // 是否暗8张
            if (tData.maxPlayer != 4) {
                tData.areaSelectMode.isDivideTeam = false;
                tData.areaSelectMode.isRandomTeam = false;
            }
            if (tData.areaSelectMode.noJoker && tData.deckNum == 3) {
                tData.areaSelectMode.hasJoker = false;
            }
            tData.areaSelectMode.isNo34 = this.createParams.isNo34; // 是否去掉3 4
            tData.isDivideTeam = tData.areaSelectMode.isDivideTeam;
            tData.hasWings = true; // 预留选项
            tData.areaSelectMode.isBiChu = tData.maxPlayer == 3; // 是否必出
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            this.pGameCode.initHandCount(this); // 初始化tData 手牌数等字段
            tData.gameCnName = "隆回霸炸弹";
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_XIN_ZHOU) {
            logger.debug("=====GameCode=====runStartGame===========忻州斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuXinzhou;
        }
        else if (tData.gameType == GAME_TYPE.HY_ER_PAO_HU_ZI) { // 衡阳二人跑胡子
            this.pGameCode = gameInstance.pGameCodePaoHuZi;
            this.pGameCode.initTable(this);
            tData.areaSelectMode.huxiType = this.createParams.huxiType; // 0 21胡息 1卡二十张
            tData.areaSelectMode.isManualCutCard = this.createParams.isManualCutCard; // 切牌
            tData.minHuxi = tData.areaSelectMode.huxiType == 0 ? 21 : 15;
            tData.areaSelectMode.diFen = "diFen" in this.createParams ? this.createParams.diFen : -1; // 分低于多少翻倍(-1.不翻 10.10分 20.20分 30.30分)
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (('trustTime' in this.createParams) && this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if ("zhuangJia" in this.createParams) {
                tData.areaSelectMode.zhuangJia = this.createParams.zhuangJia || 0; // 庄家
            }
            tData.gameCnName = "二人跑胡子";
        }
        else if (tData.gameType == GAME_TYPE.XIANG_XIANG_PAO_HU_ZI) { //湘乡跑胡子
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }

            this.pGameCode = gameInstance.pGameCodeXiangXiangPHZ;
            tData.areaSelectMode["limitWinHuxi"] = this.createParams.limitWinHuxi; //胡法 1:10胡起胡 2:15胡起胡
            tData.areaSelectMode["jifenType"] = this.createParams.jifenType; //计分 1：囤数计分 2：胡息计分
            tData.areaSelectMode["littleRed"] = this.createParams.littleRed;  //一点红 选中 true 否则 false
            tData.areaSelectMode["tianDiHu"] = this.createParams.tianDiHu; //天地胡   选中 true 否则 false
            tData.areaSelectMode["RedBlackHu"] = this.createParams.RedBlackHu; //红黑胡  选中 true 否则 false
            tData.minHuxi = tData.areaSelectMode.limitWinHuxi == 1 ? 10 : 15;
            tData.areaSelectMode["zuoZhuang"] = "zuoZhuang" in this.createParams ? this.createParams.zuoZhuang : 1; //0:随机庄   1：房主庄
            tData.areaSelectMode.maipai = "maipai" in this.createParams ? this.createParams.maipai : true;
            tData.areaSelectMode.maiPaiType = "maiPaiType" in this.createParams ? this.createParams.maiPaiType : 0;//埋牌方式  0:不埋牌   1：埋10   2：埋20
            if (!tData.areaSelectMode.maipai) tData.areaSelectMode.maiPaiType = 0;
            if (tData.areaSelectMode.maipai && tData.areaSelectMode.maiPaiType == 0) tData.areaSelectMode.maiPaiType = 2;
            tData.areaSelectMode.fullperson = "fullperson" in this.createParams ? this.createParams.fullperson : false;  //勾选满人为true，否则为false
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["faPai"] = "faPai" in this.createParams ? this.createParams.faPai : 0;
            tData.gameCnName = "湘乡跑胡子"
        }
        else if (tData.gameType == GAME_TYPE.WU_TAI_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========五台扣点=====================");
            this.pGameCode = gameInstance.pGameCodeWuTaiKouDian;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.areaSelectMode["biHu"] = this.createParams.biHu;
            tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.areaSelectMode["zhuangfen"] = this.createParams.zhuangfen;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["haohuaqidui"] = this.createParams.haohuaqidui;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["daizhuang"] = this.createParams.daizhuang;
            tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，1 不带风
            tData.areaSelectMode["zhuangfenzimofanfan"] = this.createParams.zhuangfenzimofanfan;
            tData.areaSelectMode["liuduo"] = this.createParams.liuduo;
            tData.areaSelectMode["dianpaobaogang"] = this.createParams.dianpaobaogang;
            tData.areaSelectMode["dianpaoshaogang"] = this.createParams.dianpaoshaogang;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "五台扣点";
        }
        else if (tData.gameType == GAME_TYPE.AN_HUA_PAO_HU_ZI || tData.gameType == GAME_TYPE.YY_AN_HUA_PAO_HU_ZI) {
            this.pGameCode = gameInstance.pGameCodeAnHuaPaoHuZi;
        }
        else if (tData.gameType == GAME_TYPE.MJ_ZHUO_XIA_ZI) {
            logger.debug("=====GameCode=====runStartGame===========岳阳捉虾子=====================");
            this.pGameCode = gameInstance.pGameCodeYueYangZhuoXiaZi;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;
            tData.areaSelectMode["zhuangxian"] = this.createParams.zhuangxian;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["mantianfei"] = this.createParams.mantianfei;
            tData.areaSelectMode["piaofen"] = this.createParams.piaofen;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.gameCnName = "岳阳捉虾子";
        }
        else if (tData.gameType == GAME_TYPE.BAN_BIAN_TIAN_ZHA) {
            this.pGameCode = gameInstance.pGameCodeBanBianTianZha;
        }
        else if (tData.gameType == GAME_TYPE.XIANG_TAN_PAO_HU_ZI) {
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
                tData.areaSelectMode.trustWay = this.createParams.trustWay || 0;
            }

            this.pGameCode = gameInstance.pGameCodeXiangTanPHZ;
            tData.areaSelectMode.mingwei = this.createParams.mingwei; //明畏 true： 暗畏 false
            tData.areaSelectMode.zimoaddhuxi = this.createParams.zimoaddhuxi; //自摸加3胡选中true 否则 false
            tData.areaSelectMode.isYiwushi = this.createParams.isYiwushi; //一五十选中 true 否则 false
            tData.areaSelectMode.sanshirate = this.createParams.sanshirate; //30胡翻倍选中true 否则 false
            tData.areaSelectMode.nohuaddhuxi = this.createParams.nohuaddhuxi;//荒庄5胡息
            tData.areaSelectMode.yidianhong = this.createParams.yidianhong; //一点红 选中为true 否则为false
            tData.areaSelectMode.tiandihu = this.createParams.tiandihu; //天地胡 选中为true，否则为false
            tData.areaSelectMode.daxiaozi = this.createParams.daxiaozi; //大小字 选中为true 否则false
            tData.areaSelectMode.pengpenghu = this.createParams.pengpenghu; // 碰碰胡选中为true，否则为false
            tData.areaSelectMode.hongheihu = this.createParams.hongheihu; //红黑胡选中为true否则为false
            tData.areaSelectMode.honghutype = this.createParams.honghutype; // 十红选中为true否则 13红：false
            tData.areaSelectMode.maipai = this.createParams.maipai;
            tData.areaSelectMode.maiPaiType = "maiPaiType" in this.createParams ? this.createParams.maiPaiType : 0;//埋牌方式   0：不埋  1：埋10   2：埋20
            if (!tData.areaSelectMode.maipai) tData.areaSelectMode.maiPaiType = 0;
            if (tData.areaSelectMode.maipai && tData.areaSelectMode.maiPaiType == 0) tData.areaSelectMode.maiPaiType = 2;
            tData.areaSelectMode.fullperson = "fullperson" in this.createParams ? this.createParams.fullperson : false;  //勾选满人为true，否则为false
            tData.areaSelectMode.isShowChouWei = "isShowChouWei" in this.createParams ? this.createParams.isShowChouWei : false;  //勾选满人为true，否则为false
            tData.areaSelectMode.isShowMTDieJia = "isShowMTDieJia" in this.createParams ? this.createParams.isShowMTDieJia : false; //勾选满人为true，否则
            tData.areaSelectMode.isPutLimit = "isPutLimit" in this.createParams ? this.createParams.isPutLimit : true;
            tData.areaSelectMode.firstRandomZhuang = "firstRandomZhuang" in this.createParams ? this.createParams.firstRandomZhuang : true;
            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["minHuType"] = "minHuType" in this.createParams ? this.createParams.minHuType : 0;// 胡法 0:15胡起胡 1:10胡起胡
            tData.areaSelectMode["countTunWay"] = "countTunWay" in this.createParams ? this.createParams.countTunWay : 0;// 囤数的计算方式
            // tData.areaSelectMode["diFen"] = "diFen" in this.createParams ? this.createParams.diFen : 1;// 底分的计算方式
            tData.areaSelectMode["faPai"] = "faPai" in this.createParams ? this.createParams.faPai : 0;
            tData.minHuxi = tData.areaSelectMode.minHuType == 1 ? 10 : 15;
            tData.gameCnName = "湘潭跑胡子";
        }
        else if (tData.gameType == GAME_TYPE.XIN_ZHOU_SAN_DA_ER) {
            logger.debug("=====GameCode=====runStartGame===========忻州三打二=====================");
            this.pGameCode = gameInstance.pGameCodePokerXinZhouSanDaEr;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["twoIsChangZhu"] = this.createParams.twoIsChangZhu;
            tData.areaSelectMode["zhuFuTongDa"] = this.createParams.zhuFuTongDa; // 主副同打
            tData.gameCnName = "忻州三打二";
        }
        else if (tData.gameType == GAME_TYPE.LENG_SHUI_JIANG_SHI_HU_DAO) {
            logger.debug("=====GameCode=====runStartGame===========冷水江十胡倒=====================");
            this.pGameCode = gameInstance.pGameCodeShiHuDao;
            tData.areaSelectMode["isMingWei"] = this.createParams.isMingWei;
            tData.areaSelectMode["isJiaChui"] = this.createParams.isJiaChui;
            tData.areaSelectMode["isManualCutCard"] = this.createParams.isManualCutCard;
            tData.minHuxi = 10;
            tData.areaSelectMode.fanBei = this.createParams.fanBei;//0 不翻倍 1 翻倍
            tData.areaSelectMode.fanBeiScore = this.createParams.fanBeiScore;// 低于多少翻倍
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
            tData.areaSelectMode["faPai"] = this.createParams.faPai; // 发牌速度
            tData.areaSelectMode.trustTime = -1; // 托管倒计时(-1 无)
            if (('trustTime' in this.createParams) && this.createParams.trustTime > 0) {
                tData.areaSelectMode.trustTime = this.createParams.trustTime; // +1为了客户端能走完倒计时长
            }
            if (('maiPaiNum' in this.createParams) && tData.maxPlayer == 2) {
                tData.areaSelectMode.maiPaiNum = this.createParams.maiPaiNum;
            } else {
                tData.areaSelectMode.maiPaiNum = 0;
            }
            tData.gameCnName = "冷水江十胡倒";
        }
        else if (tData.gameType == GAME_TYPE.HENG_YANG_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========衡阳三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerHengYangSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.DA_NING_SHUAI_JIN) {
            logger.debug("=====GameCode=====runStartGame===========临汾大宁摔金=====================");
            this.pGameCode = gameInstance.pGameCodeDaNingShuaiJin;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_WAI_HU_ZI) {
            this.pGameCode = gameInstance.pGameCodeYueYangWeiHuZi;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_FU_LU_SHOU) {
            this.pGameCode = gameInstance.pGameCodeYueYangFuLuShou;
            tData.minHuxi = 11;
            tData.areaSelectMode.bankType = this.createParams.bankType; //轮庄方式
            tData.areaSelectMode.piaoType = this.createParams.piaoType; //票分类型
            tData.areaSelectMode.qingZuiType = this.createParams.qingZuiType; //亲嘴几胡
            tData.areaSelectMode.isPenPenHuTwo = this.createParams.isPenPenHuTwo; //碰碰胡两个字
            tData.areaSelectMode.isZhaoGang6Xi = "isZhaoGang6Xi" in this.createParams ? this.createParams.isZhaoGang6Xi : false; // 招杠计息的值
            tData.areaSelectMode.withFlower = "withFlower" in this.createParams ? this.createParams.withFlower : false; // 是否带万能牌
            tData.areaSelectMode.isSuanFenDieJia = "isSuanFenDieJia" in this.createParams ? this.createParams.isSuanFenDieJia : true; // 算分是否叠加
            tData.areaSelectMode.piaoFlag = "piaoFlag" in this.createParams ? this.createParams.piaoFlag : 0; // 显示的飘分值，用eatfalg的方式
            tData.gameCnName = "岳阳福禄寿";
        }
        else if (tData.gameType == GAME_TYPE.FU_LU_SHOU_ER_SHI_ZHANG) {
            this.pGameCode = gameInstance.pGameCodeYueYangFuLuShouErShi;
            tData.minHuxi = 11;
            tData.areaSelectMode.bankType = this.createParams.bankType; //轮庄方式
            tData.areaSelectMode.diFen = this.createParams.diFen; //底分
            // 小胡内容
            tData.areaSelectMode.isQueYiSe = this.createParams.isQueYiSe; // 缺一色
            tData.areaSelectMode.isBanBanHu = this.createParams.isBanBanHu; // 板板胡
            tData.areaSelectMode.isLiuLiuShun = this.createParams.isLiuLiuShun; // 六六顺
            tData.areaSelectMode.isDaSiXi = this.createParams.isDaSiXi; // 大四喜
            tData.areaSelectMode.piaoFlag = this.createParams.piaoFlag; //票分类型 用eatflag的方式
            //玩法
            tData.areaSelectMode.isTongPao = this.createParams.isTongPao; //点炮通赔
            tData.areaSelectMode.isBiHu = this.createParams.isBiHu; //必胡选项
            tData.gameCnName = "岳阳福禄寿20张";
        }
        else if (tData.gameType == GAME_TYPE.XIANG_XIANG_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========湘乡三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerXiangXiangSanDaHa;
            tData.areaSelectMode["SAN_DA_HA_allowCheckCard"] = this.createParams.SAN_DA_HA_allowCheckCard;  // 允许查牌
            tData.areaSelectMode["SAN_DA_HA_touXiangXuXunWen"] = this.createParams.SAN_DA_HA_touXiangXuXunWen;// 投降需询问
            tData.areaSelectMode["SAN_DA_HA_daDaoTiQianOver"] = this.createParams.SAN_DA_HA_daDaoTiQianOver;  // 大倒提前结束
            tData.areaSelectMode["SAN_DA_HA_allowTuoLaJiXiaoDui"] = false;  // 允许拖拉机消对,岳阳三打哈参数，湘乡三打哈暂时不用
            tData.areaSelectMode["SAN_DA_HA_shuaiPaiLimitTwoCard"] = false;  // 庄家甩牌最多两张,岳阳三打哈参数，湘乡三打哈暂时不用
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;  // 底分
            tData.areaSelectMode["countScore"] = this.createParams.countScore || 1;  // 1:级数算分，默认 2:分数算分
            tData.areaSelectMode["touXiangJia10Fen"] = this.createParams.touXiangJia10Fen;  // 投降加10分
            tData.areaSelectMode["SAN_DA_HA_doubleInSingleOut"] = this.createParams.SAN_DA_HA_doubleInSingleOut;  // 双进单出
            tData.areaSelectMode["baoFuLiuShou"] = this.createParams.baoFuLiuShou;  // 报富留守
            tData.areaSelectMode["qiJiaoFen60"] = this.createParams.qiJiaoFen60;  //  60分起叫
            tData.areaSelectMode["quDiao6"] = this.createParams.quDiao6;  //  去掉6
            tData.areaSelectMode["xiaoGuangFen"] = this.createParams.xiaoGuangFen;  //  小光分
            tData.areaSelectMode["biChangZhu"] = this.createParams.biChangZhu;  // 比常主
            tData.gameCnName = "湘乡三打哈";
        }
        else if (tData.gameType == GAME_TYPE.FEN_YANG_QUE_MEN) {
            logger.debug("=====GameCode=====runStartGame===========汾阳缺门=====================");
            this.pGameCode = gameInstance.pGameCodeFenYangQueMen;
            tData.areaSelectMode["duoHu"] = false;
            tData.areaSelectMode["baoTing"] = this.createParams.baoTing;
            tData.areaSelectMode["shisanyao"] = this.createParams.shisanyao;
            tData.areaSelectMode["sanhuafeigang"] = this.createParams.sanhuafeigang;
            tData.areaSelectMode["difen"] = this.createParams.difen;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            tData.gameCnName = "汾阳缺门";
        }
        else if (tData.gameType == GAME_TYPE.HUAI_HUA_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========怀化麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHuaiHuaMJ;
            tData.areaSelectMode.huType = this.createParams.huType; //1:自摸胡 2：点炮胡
            tData.areaSelectMode.xiaohubudianpao = this.createParams.xiaohubudianpao; //选择为true否则为false
            tData.areaSelectMode.sihongzhong = this.createParams.sihongzhong ? this.createParams.sihongzhong : false; //四红中 选择为true否则为false
            tData.areaSelectMode.tianhu = this.createParams.tianhu; //天胡  选择为true否则为false
            tData.areaSelectMode.dihu = this.createParams.dihu; //地胡 选择为true否则为false
            tData.areaSelectMode.banbanhu = this.createParams.banbanhu; //板板胡 选择为true否则为false
            tData.areaSelectMode.yitiaolong = this.createParams.yitiaolong; //一条龙 选择为true否则为false
            tData.areaSelectMode.longqidui = this.createParams.longqidui; //龙七对 选择为true否则为false
            tData.areaSelectMode.siguiyi = this.createParams.siguiyi; //四归一 选择为true否则为false
            tData.areaSelectMode.jiangjianghu = this.createParams.jiangjianghu; //将将胡 选择为true否则为false
            tData.areaSelectMode.queyise = this.createParams.queyise; //缺一色 选择为true否则为false
            tData.areaSelectMode.zhuaniaotype = this.createParams.zhuaniaotype; // 0:不抓鸟 1：抓一鸟 2：抓2鸟 3：抓3鸟
            tData.areaSelectMode.zhuaniaovalue = this.createParams.zhuaniaovalue; //159抓鸟 选中为true否则为false
            tData.areaSelectMode.keganghu = this.createParams.keganghu; //可抢杠胡  选择为true否则为false
            tData.areaSelectMode.baogang = this.createParams.baogang; //包杠  选择为true否则为false
            tData.areaSelectMode.huanggangzhuang = this.createParams.huanggangzhuang; //荒庄荒杠 选择为true否则为false
            tData.areaSelectMode.qiangGangQuanBao = this.createParams.qiangGangQuanBao;
            tData.areaSelectMode.qiShou14 = this.createParams.qiShou14 || false;
            tData.areaSelectMode.touhougang = this.createParams.touhougang || false;

            tData.areaSelectMode["fanBeiScore"] = "fanBeiScore" in this.createParams ? this.createParams.fanBeiScore : 10;// 总分翻倍上限分数
            tData.areaSelectMode["fanBei"] = "fanBei" in this.createParams ? this.createParams.fanBei : 0;// 总分是否翻倍
            tData.areaSelectMode["jieSuanDiFen"] = "jieSuanDiFen" in this.createParams ? this.createParams.jieSuanDiFen : 1;// 结算底分翻倍
            tData.areaSelectMode["trustWay"] = "trustWay" in this.createParams ? this.createParams.trustWay : 0;// 托管方式
            tData.gameCnName = "怀化麻将";
        }
        else if (tData.gameType == GAME_TYPE.AN_HUA_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========安化七王麻将=====================");
            this.pGameCode = gameInstance.pGameCodeAnHuaMaJiang;
            tData.areaSelectMode.kingNum = this.createParams.kingNum;
            tData.areaSelectMode.zhuaniaoNum = this.createParams.zhuaniaoNum;
            tData.areaSelectMode.keganghu = this.createParams.keganghu;
            tData.areaSelectMode.wangdaiying = this.createParams.wangdaiying;
            tData.areaSelectMode.isDuoHu = this.createParams.isDuoHu;
            tData.areaSelectMode.zhuangxianfen = this.createParams.zhuangxianfen;
            if ("zhongNiaoType" in this.createParams)
                tData.areaSelectMode["zhongNiaoType"] = this.createParams.zhongNiaoType; // 是否开启159zhogniao
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode.beiShu = this.createParams.beiShu ? this.createParams.beiShu : "1";
            tData.areaSelectMode["trustWay"] = "trustWay" in this.createParams ? this.createParams.trustWay : 0;
            tData.gameCnName = "安化七王麻将";
        }
        else if (tData.gameType == GAME_TYPE.JING_LE_KOU_DIAN) {
            logger.debug("=====GameCode=====runStartGame===========静乐扣点=====================");
            this.pGameCode = gameInstance.pGameCodeJingleKoudian;
            tData.areaSelectMode["daifeng"] = this.createParams.daifeng;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.areaSelectMode["diejia"] = this.createParams.diejia;
            tData.areaSelectMode["haohuaqidui"] = this.createParams.haohuaqidui;
            tData.areaSelectMode["bihu"] = this.createParams.bihu;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["ziyise"] = this.createParams.ziyise;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "静乐扣点";
        }
        else if (tData.gameType == GAME_TYPE.DA_TONG_GUAI_SAN_JIAO) {
            logger.debug("=====GameCode=====runStartGame===========大同拐三角=====================");
            this.pGameCode = gameInstance.pGameCodeDaTongGuaiSanJiao;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;                     // 底分
            tData.areaSelectMode["fangZuoBi"] = this.createParams.fangZuoBi;                  // 防作弊
            tData.areaSelectMode["qiDui"] = this.createParams.qiDui;                          // 七对
            tData.areaSelectMode["guoHuZhiNengZiMo"] = this.createParams.guoHuZhiNengZiMo;    // 过胡只能自摸
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;                  // 轮庄
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "大同拐三角";
        }
        else if (tData.gameType == GAME_TYPE.LUAN_GUA_FENG) {
            logger.debug("=====GameCode=====runStartGame===========乱刮风=====================");
            this.pGameCode = gameInstance.pGameCodeLuanguafeng;
            tData.areaSelectMode["fangzuobi"] = this.createParams.fangzuobi;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["gangpaisuanfen"] = this.createParams.gangpaisuanfen;
            tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "乱刮风";
        }
        else if (tData.gameType == GAME_TYPE.CHEN_ZHOU_ZI_PAI || tData.gameType == GAME_TYPE.GUI_YANG_ZI_PAI) {
            this.pGameCode = gameInstance.pGameCodePaoHuZiChenZhouZiPai;
        }
        else if (tData.gameType == GAME_TYPE.AN_HUA_MA_JIANG_SW) {
            logger.debug("=====GameCode=====runStartGame===========安化四王麻将=====================");
            this.pGameCode = gameInstance.pGameCodeAHSiWangMaJiang;
            tData.areaSelectMode.wangdaiying = this.createParams.wangdaiying;
            tData.areaSelectMode.gangKaiNum = this.createParams.gangKaiNum;
            tData.areaSelectMode.zhuaniaoNum = this.createParams.zhuaniaoNum;
            if ("fenZhuangXian" in this.createParams)
                tData.areaSelectMode.fenZhuangXian = this.createParams.fenZhuangXian; // 是否开启庄闲分
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.gameCnName = "安化四王麻将";
        }
        else if (tData.gameType == GAME_TYPE.DA_TONG_ZHA_GU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========大同扎股子=====================");
            this.pGameCode = gameInstance.pGameCodePokerZhaGuZiDaTong;
            tData.areaSelectMode.showHandNum = this.createParams.showHandNum;
            tData.areaSelectMode.difen = this.createParams.difen || 1;
            tData.gameCnName = "大同扎股子";
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_NIU_SHI_BIE) {
            logger.debug("=====GameCode=====runStartGame===========牛十别=====================");
            this.pGameCode = gameInstance.pGameCodePokerNiuShiBie;
            tData.areaSelectMode["sandaidui"] = this.createParams.sandaidui;  // 是否可以三代对子(bool)
            tData.areaSelectMode["feijidailiandui"] = this.createParams.feijidailiandui;  // 飞机是否可以代连对(bool)
            tData.areaSelectMode["zhuaweifen"] = this.createParams.zhuaweifen;  // 是否抓尾分(bool)
            tData.areaSelectMode["teammode"] = this.createParams.teammode;  // 分队模式(1:摸队，2：铁队)
            tData.areaSelectMode["niushibie_difen"] = this.createParams.niushibie_difen || 1;  // 底分（1,2,3,30/50）
            tData.gameCnName = "岳阳牛十别";
        }
        else if (tData.gameType == GAME_TYPE.NING_XIANG_PAO_HU_ZI) {
            this.pGameCode = gameInstance.pGameCodePaoHuZiNingXiang;
        }
        else if (tData.gameType == GAME_TYPE.NING_XIANG_KAI_WANG) {
            this.pGameCode = gameInstance.pGameCodeNingXiangKaiWang;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_PENG_HU) {
            this.pGameCode = gameInstance.pGameCodePaoHuZiPengHu;
        }
        else if (tData.gameType == GAME_TYPE.YI_YANG_WAI_HU_ZI) {
            this.pGameCode = gameInstance.pGameCodeYiYangWaiHuZi;
        }
        else if (tData.gameType == GAME_TYPE.NAN_XIAN_GUI_HU_ZI) {
            this.pGameCode = gameInstance.pGameCodeNanXianGuiHuZi;
        }
        else if (tData.gameType == GAME_TYPE.YUAN_JIANG_GUI_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========沅江鬼胡子=====================");
            this.pGameCode = gameInstance.pGameCodeYuanJiangGuiHuZi;
        }
        else if (tData.gameType == GAME_TYPE.TAO_JIANG_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========桃江麻将=====================");
            this.pGameCode = gameInstance.pGameCodeTaoJiangMaJiang;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_YUAN_JIANG_QIAN_FEN) {
            logger.debug("=====GameCode=====runStartGame===========岳阳沅江千分=====================");
            this.pGameCode = gameInstance.pGameCodeYueYangYuanJiangQianFen;
        }
        else if (tData.gameType == GAME_TYPE.DIAN_TUO) {
            logger.debug("=====GameCode=====runStartGame===========掂坨=====================")
            this.pGameCode = gameInstance.pGameCodeDianTuo;
            //tData.deckNum = 2; //固定2副牌
            tData.areaSelectMode.nScoreLine = this.createParams.nScoreLine;     //分数
            tData.areaSelectMode.isFullCard = this.createParams.isFullCard;       //去掉3、4是false
            tData.areaSelectMode.isSeeTeamCard = this.createParams.isSeeTeamCard; //看队友牌
            tData.areaSelectMode.isRdTeam = this.createParams.isRdTeam;           //随机分组
            //不打港
            if ("isBuDaGang" in this.createParams) {
                tData.areaSelectMode.isBuDaGang = this.createParams.isRdTeam ? false : this.createParams.isBuDaGang;
            }
            else {
                tData.areaSelectMode.isBuDaGang = false;
            }
            tData.areaSelectMode.isNoShunZi = "isNoShunZi" in this.createParams ? this.createParams.isNoShunZi : false;     //是否不打顺子
            tData.areaSelectMode.isZhaNoKing = "isZhaNoKing" in this.createParams ? this.createParams.isZhaNoKing : false;  //是否勾选了炸弹不带王
            tData.areaSelectMode.isHuaSeValid = "isHuaSeValid" in this.createParams ? this.createParams.isHuaSeValid : false; //5-10-k是否区分花色
            tData.areaSelectMode.isZuiHouShaoDai = "isZuiHouShaoDai" in this.createParams ? this.createParams.isZuiHouShaoDai : false;  //是否勾选 仅最后飞机三条可少带
            if (tData.maxPlayer == 4) {
                tData.areaSelectMode.isBombScore = this.createParams.nScoreLine == 100 ? this.createParams.isBombScore : false;   //炸弹有喜
                tData.areaSelectMode.isRedBlackScore = this.createParams.nScoreLine == 100 ? this.createParams.isRedBlackScore : false; //四红四黑
            }
            else {
                tData.areaSelectMode.isBombScore = this.createParams.nScoreLine == 60 ? this.createParams.isBombScore : false;   //炸弹有喜
                tData.areaSelectMode.isRedBlackScore = this.createParams.nScoreLine == 60 ? this.createParams.isRedBlackScore : false; //四红四黑
            }

            tData.areaSelectMode.nTianZhaScore = 0;        //天炸分
            if (tData.areaSelectMode.isBombScore) {
                tData.areaSelectMode.nTianZhaScore = this.createParams.nTianZhaScore ? this.createParams.nTianZhaScore : 3;  //炸弹有喜情况下默认3分
            }
            tData.areaSelectMode.isShowRemainCards = this.createParams.isShowRemainCards;  //是否显示剩余牌
            tData.areaSelectMode.isSanFuPai = "isSanFuPai" in this.createParams ? this.createParams.isSanFuPai : false; //新增.三副牌
            tData.deckNum = tData.areaSelectMode.isSanFuPai ? 3 : 2; //几副牌
            tData.areaSelectMode.jieSuanDiFen = this.createParams.jieSuanDiFen || 1;// 结算底分
            //logger.debug("创建房间数据...", JSON.stringify(tData.areaSelectMode));
            this.pGameCode.initHandCount(this); // 初始化tData 手牌数等字段
            tData.gameCnName = "掂坨";
            tData.hasWings = true;      //默认就是带牌
        }
        else if (tData.gameType == GAME_TYPE.YI_YANG_MA_JIANG) {
            logger.debug("=====GameCode=====runStartGame===========益阳麻将=====================")
            this.pGameCode = gameInstance.pGameCodeYiYang;
        }
        else if (tData.gameType == GAME_TYPE.CHEN_ZHOU) {
            logger.debug("=====GameCode=====runStartGame===========郴州麻将=====================");
            this.pGameCode = gameInstance.pGameCodeChenzhou;
            tData.areaSelectMode["fanBei"] = this.createParams.fanBei;
            tData.areaSelectMode["fanBeiScore"] = this.createParams.fanBeiScore;
            tData.areaSelectMode["zhuaniao"] = this.createParams.zhuaniao;
            tData.areaSelectMode["buzhongsuanquanzhong"] = this.createParams.buzhongsuanquanzhong;
            tData.areaSelectMode["dianpao"] = this.createParams.dianpao;
            tData.areaSelectMode["hongzhong"] = this.createParams.hongzhong;
            tData.areaSelectMode["qidui"] = this.createParams.qidui;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["niaofen"] = this.createParams.niaofen || 1;
            tData.areaSelectMode["duoHu"] = this.createParams.duoHu;
            tData.areaSelectMode["piaofen"] = this.createParams.piaofen;
            tData.areaSelectMode["zhuang"] = this.createParams.zhuang;
            tData.areaSelectMode["dahu"] = this.createParams.dahu;
            tData.gameCnName = "郴州麻将";
        }
        else if (tData.gameType == GAME_TYPE.YUAN_JIANG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========沅江麻将=====================");
            this.pGameCode = gameInstance.pGameCodeYuanJiangMJ;
        }
        else if (tData.gameType == GAME_TYPE.YUE_YANG_DA_ZHA_DAN) { // 打炸弹
            this.pGameCode = gameInstance.pGameCodeDaZhaDan;
        }
        else if (tData.gameType == GAME_TYPE.CHEN_ZHOU_MAO_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========郴州毛胡子=====================");
            this.pGameCode = gameInstance.pGameCodeChenZhouMaoHuZi;
        }
        else if (tData.gameType == GAME_TYPE.ZHU_ZHOU_DA_MA_ZI) {
            logger.debug("=====GameCode=====runStartGame===========株洲打码子=====================");
            this.pGameCode = gameInstance.pGameCodePokerDaMaZi;
        }
        else if (tData.gameType == GAME_TYPE.NING_XIANG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========宁乡麻将=====================");
            this.pGameCode = gameInstance.pGameCodeNingXiangMJ;
        }
        else if (tData.gameType == GAME_TYPE.XIANG_XI_2710) {
            logger.debug("=====GameCode=====runStartGame===========湘西2710=====================");
            this.pGameCode = gameInstance.pGameCodeXiangXi2710;
        }
        else if (tData.gameType == GAME_TYPE.CHANG_DE_PAO_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========常德跑胡子=====================");
            this.pGameCode = gameInstance.pGameCodePaoHuZiChangDe;
        }
        else if (tData.gameType == GAME_TYPE.YUAN_LING_PAO_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========沅陵跑胡子=====================");
            this.pGameCode = gameInstance.pGameCodePaoHuZiYuanLing;
        }
        else if (tData.gameType == GAME_TYPE.SHI_MEN_PAO_HU_ZI) { //CENA
            logger.debug("=====GameCode=====runStartGame===========石门跑胡子=====================");
            this.pGameCode = gameInstance.pGameCodePaoHuZiShiMen;
        }
        else if (tData.gameType == GAME_TYPE.HAN_SHOU_PAO_HU_ZI) {
            logger.debug("=====GameCode=====runStartGame===========汉寿跑胡子=====================");
            this.pGameCode = gameInstance.pGameCodePaoHuZiHanShou;
        }
        else if (tData.gameType == GAME_TYPE.CHAO_GU_MJ) {
            logger.debug("=====GameCode=====runStartGame===========岳阳炒股麻将=====================");
            this.pGameCode = gameInstance.pGameCodeChaoguMJ;
        }
        else if (tData.gameType == GAME_TYPE.NAN_XIAN_MJ) {
            logger.debug("=====GameCode=====runStartGame===========岳阳南县麻将=====================");
            this.pGameCode = gameInstance.pGameCodeNanXianMJ;
        }
        else if (tData.gameType == GAME_TYPE.YUN_CHENG_TIE_JIN) {
            logger.debug("=====GameCode=====runStartGame===========山西运城贴金麻将=====================");
            this.pGameCode = gameInstance.pGameCodeYunChengTieJin;
        }
        else if (tData.gameType == GAME_TYPE.XIANG_SHUI_MJ) {
            logger.debug("=====GameCode=====runStartGame===========江苏响水麻将=====================");
            this.pGameCode = gameInstance.pGameCodeXiangShuiMJ;
        }
        else if (tData.gameType == GAME_TYPE.QU_TANG_23_ZHANG) {
            logger.debug("=====GameCode=====runStartGame===========南通曲塘23张=====================");
            this.pGameCode = gameInstance.pGameCodeQuTang23Zhang;
        }
        else if (tData.gameType == GAME_TYPE.HE_JIN_KUN_JIN) {
            logger.debug("=====GameCode=====runStartGame===========山西河津捆金麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHeJinKunJin;
        }
        else if (tData.gameType == GAME_TYPE.YUN_CHENG_FENG_HAO_ZI) {
            logger.debug("=====GameCode=====runStartGame===========运城风耗子=====================");
            this.pGameCode = gameInstance.pGameCodeYunchengFenghaozi;
            tData.areaSelectMode["lunzhuang"] = this.createParams.lunzhuang;
            // tData.areaSelectMode["playType"] = this.createParams.playType; // 0 经典，1 不带风，2 带耗子  3 随机耗子 4 双耗子
            tData.areaSelectMode["playType"] = 2; // 默认为 2风耗子 玩法
            tData.areaSelectMode["guoHuZiMo"] = this.createParams.guoHuZiMo;
            tData.areaSelectMode["baogang"] = this.createParams.baogang;
            tData.areaSelectMode["difen"] = this.createParams.difen || 1;
            tData.areaSelectMode["biHu"] = false;
            tData.areaSelectMode["dahu"] = false;
            tData.areaSelectMode["shisanyao"] = false;
            tData.areaSelectMode["zhuangfen"] = false;
            tData.areaSelectMode["zhuangfenzimofanfan"] = false;
            tData.areaSelectMode["duoHu"] = false;
            tData.gameCnName = "运城风耗子";
        }
        else if (tData.gameType == GAME_TYPE.JI_SHAN_NIU_YE_ZI) {
            logger.debug("=====GameCode=====runStartGame===========稷山扭叶子=====================");
            this.pGameCode = gameInstance.pGameCodeJiShanNiuYeZi;
        }
        else if (tData.gameType == GAME_TYPE.SHAO_YANG_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========邵阳/天天三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerShaoYangSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.XIANG_TAN_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========湘潭三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerXiangTanSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.AN_XIANG_WEI_MA_QUE) {
            logger.debug("=====GameCode=====runStartGame===========安乡偎麻雀=====================");
            this.pGameCode = gameInstance.pGameCodeAnXiangWeiMaQue;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_PU_DING_MJ) {
            logger.debug("=====GameCode=====runStartGame===========贵州普定麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouPuDing;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_AN_SHUN_MJ) {
            logger.debug("=====GameCode=====runStartGame===========贵州安顺麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouAnShun;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_XMY_GUI_YANG_ZHUO_JI) {
            logger.debug("=====GameCode=====runStartGame===========贵州新麻友贵阳捉鸡麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouXMYGuiYangZhuoJi;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_GUI_YANG_ZHUO_JI) {
            logger.debug("=====GameCode=====runStartGame===========贵州贵阳捉鸡麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouGuiYangZhuoJi;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_SAN_DING_LIANG_FANG || tData.gameType === GAME_TYPE.GUI_ZHOU_LIANG_DING_LIANG_FANG) {
            logger.debug("=====GameCode=====runStartGame===========贵州三丁两房/两丁两房麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouSanDingLiangFang;
        }
        else if (tData.gameType == GAME_TYPE.SAN_DA_HA_NEW) {
            logger.debug("=====GameCode=====runStartGame===========新三打哈=====================");
            this.pGameCode = gameInstance.pGameCodePokerSanDaHaNew;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_SAN_DING_GUAI || tData.gameType === GAME_TYPE.GUI_ZHOU_ER_DING_GUAI) {
            logger.debug("=====GameCode=====runStartGame===========贵州三丁拐/二丁拐麻将====================");
            this.pGameCode = gameInstance.pGameCodeGuiZhouSanDingGuai;
        }
        else if (tData.gameType == GAME_TYPE.DOU_DI_ZHU_GZ) {
            logger.debug("=====GameCode=====runStartGame===========贵州斗地主=====================");
            this.pGameCode = gameInstance.pGameCodeDoudizhuGZ;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_MEN_HU_XUE_LIU) {
            logger.debug("=====GameCode=====runStartGame===========贵州闷胡血流====================");
            this.pGameCode = gameInstance.pGameCodeMenHuXueLiu;
        }
        else if (tData.gameType == GAME_TYPE.YONG_LI_SAN_DA_HA) {
            logger.debug("=====GameCode=====runStartGame===========永利三打哈（衡阳三打哈）=====================");
            this.pGameCode = gameInstance.pGameCodePokerYongLiSanDaHa;
        }
        else if (tData.gameType == GAME_TYPE.POKER_96) {
            logger.debug("=====GameCode=====runStartGame===========96扑克=====================");
            this.pGameCode = gameInstance.pGameCode96Poker;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_DU_YUN_MJ) { // 都匀麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouDuYun;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_ZUN_YI_MJ) { // 遵义
            this.pGameCode = gameInstance.pGameCodeGuiZhouZunYi;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_LIANG_DING_YI_FANG) { // 贵州两丁一房
            this.pGameCode = gameInstance.pGameCodeGuiZhouLiangDingYiFang;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_AN_LONG_MJ) { // 安龙麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouAnLong;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_XING_YI_MJ) { // 兴义麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouXingYi;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_WENG_AN_MJ) { // 瓮安麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouWengAn;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_KAI_LI_MJ) { // 凯里麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouKaiLi;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_ELEVEN) { // 11张跑得快
            this.pGameCode = gameInstance.pGameCodePaodekuaiEleven;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_LI_PING_MJ) { // 黎平麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouLiPing;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_LIU_PAN_SHUI_MJ) { // 六盘水麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouLiuPanShui;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_TIAN_ZHU_MJ) { // 天柱麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouTianZhu;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_TONG_REN_MJ) { // 铜仁麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouTongRen;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_RONG_JIANG_MJ) { // 榕江麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouRongJiang;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_REN_HUAI_MJ) { // 仁怀麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouRenHuai;
        }
        else if (tData.gameType == GAME_TYPE.XU_PU_LAO_PAI) { // 溆浦老牌麻将
            this.pGameCode = gameInstance.pGameCodeXuPuLaoPaiMJ;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_BI_JIE_MJ) { // 毕节麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouBiJie;
        }
        else if (tData.gameType == GAME_TYPE.ER_REN_YI_FANG_MJ) { // 二人一房麻将（急速8张麻将）
            this.pGameCode = gameInstance.pGameCodeErRenYiFangMJ;
        }
        else if (tData.gameType == GAME_TYPE.XU_PU_PAO_HU_ZI) { //溆浦跑胡子
            this.pGameCode = gameInstance.pGameCodePaoHuZiXuPu;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_SUI_YANG_MJ) { // 绥阳麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouSuiYang;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_JIN_SHA_MJ) { // 金沙麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouJinSha;
        }
        else if (tData.gameType == GAME_TYPE.GUI_ZHOU_XUE_ZHAN_DAO_DI_MJ) { // 血战到底麻将
            this.pGameCode = gameInstance.pGameCodeGuiZhouXueZhanDaoDi;
        }
        else if (tData.gameType == GAME_TYPE.YONG_ZHOU_BAO_PAI) {
            this.pGameCode = gameInstance.pGameCodePokerYongZhouBaoPai;
        }
        else if (tData.gameType == GAME_TYPE.ZHUO_HAO_ZI) {
            this.pGameCode = gameInstance.pGameCodeZhuoHaoZi;
        }
        else if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_XIANG_SHUI) {
            this.pGameCode = gameInstance.pGameCodePaodekuaiXiangShui;
        }
        else if (tData.gameType == GAME_TYPE.JING_ZHOU_MA_JIANG) {    // 邵阳 荆州麻将
            this.pGameCode = gameInstance.pGameCodeJingZhou;
            this.pGameCode.initTable(this); // 初始化tData
        }
        else if (tData.gameType == GAME_TYPE.EN_SHI_MA_JIANG) {    // 湖北 恩施麻将
            this.pGameCode = gameInstance.pGameCodeEnShi;
        }
        else if (tData.gameType == GAME_TYPE.YANG_XIN_MA_JIANG) {    // 湖北：阳新麻将
            this.pGameCode = gameInstance.pGameCodeYangXin;
        }
        else if (tData.gameType == GAME_TYPE.YI_CHANG_XUE_LIU_MJ) {
            logger.debug("=====GameCode=====runStartGame===========宜昌血流成河麻将=====================");
            this.pGameCode = gameInstance.pGameCodeYiChangXueLiuMJ;
        }
        else if (tData.gameType == GAME_TYPE.DA_YE_ZI_PAI) { // 湖北 大冶字牌
            this.pGameCode = gameInstance.pGameCodeDaYeZiPai;
        }
        else if (tData.gameType == GAME_TYPE.HU_BEI_YI_JIAO_LAI_YOU) {
            logger.debug("=====GameCode=====runStartGame===========湖北：一脚癞油=====================");
            this.pGameCode = gameInstance.pGameCodeYiJiaoLaiYouHuBei;
        }
        else if (tData.gameType == GAME_TYPE.HU_BEI_JING_SHAN_MJ) {
            logger.debug("=====GameCode=====runStartGame===========京山麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHuBeiJingShanMj;
        }
        else if (tData.gameType == GAME_TYPE.CHONG_YANG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：崇阳麻将=====================");
            this.pGameCode = gameInstance.pGameCodeChongYangMJ;
        }
        else if (tData.gameType === GAME_TYPE.TONG_CHENG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：通城麻将=====================");
            this.pGameCode = gameInstance.pGameCodeTongChengMJ;
        }
        else if (tData.gameType === GAME_TYPE.DA_YE_KAI_KOU_FAN) {
            logger.debug("=====GameCode=====runStartGame===========湖北：大冶开口番=====================");
            this.pGameCode = gameInstance.pGameCodeDaYeKaiKouFan;
        }
        else if (tData.gameType == GAME_TYPE.CHUO_XIA_ZI) {
            logger.debug("=====GameCode=====runStartGame===========湖北：戳虾子=====================");
            this.pGameCode = gameInstance.pGameCodeChuoXiaZi;
        }
        else if (tData.gameType === GAME_TYPE.HU_BEI_HUA_PAI) { //湖北花牌
            this.pGameCode = gameInstance.pGameCodeHuBeiHuaPai;
        }
        else if (tData.gameType == GAME_TYPE.HUANG_SHI_HH_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：黄石晃晃麻将=====================");
            this.pGameCode = gameInstance.pGameCodeHuangShiHHMJ;
        }
        else if (tData.gameType === GAME_TYPE.TONG_SHAN_HH_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：通山晃晃麻将=====================");
            this.pGameCode = gameInstance.pGameCodeTongShanHHMJ;
        } else if (tData.gameType == GAME_TYPE.JIANG_LING_HONG_ZHONG) {
            logger.debug("=====GameCode=====runStartGame===========湖北：江陵红中麻将=====================");
            this.pGameCode = gameInstance.pGameCodeJiangLingHongZhong;
        }
        else if (tData.gameType === GAME_TYPE.HONG_ZHONG_LAI_ZI_GANG) {
            logger.debug("=====GameCode=====runStartGame===========湖北：红中癞子杠=====================");
            this.pGameCode = gameInstance.pGameCodeHongZhongLaiZiGang;
        } else if (tData.gameType == GAME_TYPE.QI_CHUN_GD_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：蕲春广东麻将=====================");
            this.pGameCode = gameInstance.pGameCodeQiChunGuangDongMJ;
        }
        else if (tData.gameType === GAME_TYPE.QI_CHUN_HH_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：蕲春晃晃=====================");
            this.pGameCode = gameInstance.pGameCodeQiChunHH;
        }
        else if (tData.gameType == GAME_TYPE.GONG_AN_HUA_PAI) {
            this.pGameCode = gameInstance.pGameCodeGongAnHuaPai;
        }
        else if (tData.gameType === GAME_TYPE.SHI_SHOU_AI_HUANG) {
            logger.debug("=====GameCode=====runStartGame===========湖北：石首捱晃=====================");
            this.pGameCode = gameInstance.pGameCodeShiShouAiHuang;
        }
        else if (tData.gameType == GAME_TYPE.QIAN_JIANG_HH_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：潜江晃晃麻将=====================");
            this.pGameCode = gameInstance.pGameCodeQianJiangHHMJ;
        }
        else if (tData.gameType === GAME_TYPE.WU_XUE_MJ) {
            logger.debug("=====GameCode=====runStartGame===========湖北：武穴麻将=====================");
            this.pGameCode = gameInstance.pGameCodeWuXueMJ;
        }
        else if (tData.gameType == GAME_TYPE.XIAO_GAN_KA_WU_XING) {
            logger.debug("=====GameCode=====runStartGame===========湖北：孝感卡五星=====================");
            this.pGameCode = gameInstance.pGameCodeXiaoGanKaWuXingMJ;
        }
        else if (tData.gameType == GAME_TYPE.QI_CHUN_HONG_ZHONG_GANG) {
            logger.debug("=====GameCode=====runStartGame===========湖北：蕲春红中杠=====================");
            this.pGameCode = gameInstance.pGameCodeQiChunHongZhongGang;
        }
        else if (tData.gameType == GAME_TYPE.TONG_CHENG_GE_ZI_PAI) {
            this.pGameCode = gameInstance.pGameCodeTongChengGeZiPai;
        }
        else if (tData.gameType == GAME_TYPE.QI_CHUN_DA_GONG) {
            logger.debug("=====GameCode=====runStartGame===========湖北：蕲春打拱=====================");
            this.pGameCode = gameInstance.pGameCodeQiChunDaGong;
        }
        else if (tData.gameType == GAME_TYPE.DA_YE_DA_GONG) {
            logger.debug("=====GameCode=====runStartGame===========大冶打拱=====================");
            this.pGameCode = gameInstance.pGameCodeDaYeDaGong;
        }
        else if (tData.gameType == GAME_TYPE.EN_SHI_SHAO_HU) {
            logger.debug("=====GameCode=====runStartGame===========恩施绍胡=====================");
            this.pGameCode = gameInstance.pGameCodeEnShiShaoHu;
        }
        else if (tData.gameType == GAME_TYPE.SUI_ZHOU_KA_WU_XING) {
            logger.debug("=====GameCode=====runStartGame===========湖北：随州卡五星=====================");
            this.pGameCode = gameInstance.pGameCodeSuiZhouKaWuXingMJ;
        }
        else if (tData.gameType == GAME_TYPE.QIAN_JIANG_QIAN_FEN) {
            logger.debug("=====GameCode=====runStartGame===========潜江千分=====================");
            this.pGameCode = gameInstance.pGameCodeQianJiangQianFen;
        }
        else if (tData.gameType == GAME_TYPE.YONG_ZHOU_LAO_CHUO) {
            this.pGameCode = gameInstance.pGameCodeYongZhouLaoChuo;
        }
        else if (tData.gameType == GAME_TYPE.NIU_NIU) {
            this.pGameCode = gameInstance.pGameCodePokerNiuniu;
        }
        else if (tData.gameType == GAME_TYPE.ZHA_JIN_HUA) {
            this.pGameCode = gameInstance.pGameCodePokerZhaJinHua;
        }
        else if (tData.gameType == GAME_TYPE.LAO_YAN_CAI) {
            this.pGameCode = gameInstance.pGameCodePokerLaoYanCai;
        }
        else if (tData.gameType == GAME_TYPE.ZHAO_TONG_MJ) {
            logger.debug("=====GameCode=====runStartGame===========昭通麻将=====================");
            this.pGameCode = gameInstance.pGameCodeZTMJ;
        }
        else if(tData.gameType == GAME_TYPE.GE_JIU){
            logger.debug("=====GameCode=====runStartGame===========个旧麻将=====================");
            this.pGameCode = gameInstance.pGameCodeGeJiuMJ;
        }
        else if (tData.gameType == GAME_TYPE.CHUN_TIAN) {
            logger.debug("=====GameCode=====runStartGame===========春天扑克===================");
            this.pGameCode = gameInstance.pGameCodeChunTian;
        }
        else {
            logger.error("pGameCode 初始化错误", tData.gameType);
            return;
        }
        if (this.pGameCode.initAreaSelectMode) {
            this.pGameCode.initAreaSelectMode(this);
        }
        if (tData.areaSelectMode['difen']) {
            tData.areaSelectMode['difen'] = Number(tData.areaSelectMode['difen']);
        }
        //用于降低免扣比例功能记录数据进行分析
        this.changeCardRecord = [];
    };
  // 金币场算分
    Table.prototype.goldfieldScore = function() {
        return;
    };
    Table.prototype.updateTableInfo = function (pinfo, callback) {
        // this.players[pinfo.uid].info.gold = pinfo.gold;
        // this.players[pinfo.uid].info.jipaiqi1 = pinfo.jipaiqi1;
        // this.players[pinfo.uid].info.jipaiqi2 = pinfo.jipaiqi2;
        // this.players[pinfo.uid].info.doubleCard = pinfo.doubleCard;
        // if (this.players[pinfo.uid].info.jipaiqi2 > 0 && this.tData.jipaiqi.indexOf(pinfo.uid) == -1) {
        //     this.tData.jipaiqi.push(pinfo.uid);
        // }
        callback();
    };

    Table.prototype.initPlayer = function (pl, msg) {
        if (!pl) {
            return;
        }
        pl.init();
        pl.winall = 0;                          //累计赢
        pl.shuffleNum = 0;                      //累计洗牌
        pl.linkZhuang = 0; 					    //连庄次数
        pl.zimoTotal = 0;					    //自摸总数
        pl.jiepaoTotal = 0;					    //接炮总数
        pl.dianpaoTotal = 0;					//点炮总数
        pl.minggangTotal = 0;				    //明杠总数
        pl.angangTotal = 0;					    //暗杠总数
        pl.roomStatistics = [0, 0, 0, 0, 0];    //房间统计数据
        pl.tableMsg = [];                       //玩家每一局的分数
        pl.locationApps = [];                   //定位修改app

        var StartPlayerCount = 2;
        if (this.pGameCode.StartPlayerCount) {  //指定游戏开始人数， 主要是牛牛， 金花， 捞腌菜
            StartPlayerCount = this.pGameCode.StartPlayerCount(this);
        }
        // if (this.tData.tState == TableState.waitReady && this.PlayerCount() == this.createParams.maxPlayer) {
        if ((this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.tData.tState == TableState.waitReady && this.PlayerCount() == StartPlayerCount) {
            this.AllPlayerRun(function (p) {
                p.mjState = TableState.waitReady;
            });
            this.NotifyAll("waitReady", {});
        }
        else
        {
            if (this.tData.tState == TableState.waitReady && this.PlayerCount() == this.createParams.maxPlayer) {
                this.AllPlayerRun(function(p) {
                    p.mjState = TableState.waitReady;
                });
                this.NotifyAll("waitReady", {});
            }
        }
        if (this.tData.gameType == GAME_TYPE.YUAN_JIANG_GUI_HU_ZI) {
            logger.debug("lijm=============true>>>>>>>>>>>");
        }
        if (this.tData.gameType == GAME_TYPE.HE_JIN_KUN_JIN) {
            logger.debug(" HE_JIN_KUN_JIN =============true>>>>>>>>>>>");
        }
        if (this.pGameCode.initPlayerOnce) {
            this.pGameCode.initPlayerOnce(pl, this);
        }
        if (this.tData.freeBegin) {
            this.tData.freeBegin = 0;
            if (this.freeBeginTimer) {
                clearTimeout(this.freeBeginTimer);
            }
        }
        if (this.tData.uids.length < this.tData.maxPlayer) {
            for (var i = this.tData.maxPlayer - this.tData.uids.length; i; i--) {
                this.tData.uids.push(0);
            }
        }
        if (this.tData.uids.indexOf(pl.uid) < 0) {
            for (var i = 0; i < this.tData.uids.length; i++) {
                if (this.tData.uids[i] == 0) {
                    this.tData.uids[i] = pl.uid;
                    break;
                }
            }
        }
        if (this.tData.uidsQueue.indexOf(pl.uid) < 0) {
            this.tData.uidsQueue.push(pl.uid);
        }

        //这个值针对牛牛游戏中途加入状态
        var centerJoinNN = false;
        if ((this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.tData.rungingUids.indexOf(pl.uid) < 0 && !this.tData.gameRuning) {
            this.tData.rungingUids.push(pl.uid);
            pl.roomStatistics = [0, 0, 0, 0, 0];
        } else {
            centerJoinNN = true;
            pl.centerJoinNN = true;
        }

        if ((this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.tData.rungingUids.length >= 2) {
            pl.mjState = TableState.waitReady;
        }

        if (msg && msg.withoutNotify) {
            return;
        }
        this.NotifyAll('addPlayer', {
            player: {
                info: pl.info,
                onLine: true,
                firstFlower8: pl.firstFlower8,
                mjState: pl.mjState,
                winall: pl.winall,
                zimoTotal: pl.zimoTotal,
                minggangTotal: pl.minggangTotal,
                angangTotal: pl.angangTotal,
                score_draw: pl.score_draw, // 打筒子
                score_spclType: pl.score_spclType, // 打筒子
                centerJoinNN: centerJoinNN //中途加入
            },
            tData: JSON.parse(JSON.stringify(this.tData))
        });
    };

    Table.prototype.endVipTable = function () {
        this.deleteSnapshoot();
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        if (this.dissolveTimer) {
            clearTimeout(this.dissolveTimer);
            this.dissolveTimer = null;
        }
        var uids = Object.keys(this.players);
        var dids = {};
        for (var i = 0; i < uids.length; i++) {
            var did = app.GetServerById('pkplayer', uids[i]).id;
            if (!dids[did]) dids[did] = [];
            dids[did].push(uids[i]);
        }
        for (var did in dids) {
            app.rpc.pkplayer.Rpc.endVipTable(did, this.tableid, dids[did], null, emptyFunction);
        }
        if (this.createParams.owner > 100000 && this.mjlog.length > 2) {
            redisUtil.client().publish('QUEUE_WRITE_PLAYBACK_GAME', JSON.stringify({ filename: this.createParams.owner + '_' + this.tableid + '.json', data: this.mjlog }));
        }

        if (!this.createParams.clubId && this.createParams.owner > 100000) {
            app.rpc.pkplayer.Rpc.endVipTable(app.GetServerById('pkplayer', this.createParams.owner).id, this.tableid, null, this.createParams.owner, emptyFunction);
        }
        if (this.createParams.clubId) {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'end', { ruleId: this.createParams.ruleId, roomNum: this.tableid }, emptyFunction);
        }
        setTimeout(() => {
            if (this.createParams.owner > 100000) {
                redisUtil.client().del('ROOM_' + this.tableid);
            }
            this.AllPlayerRun((pl) => {
                this.RemovePlayer(pl.uid);
            });
            this.AllWatcherRun((pl) => {
                this.RemoveWatcher(pl.uid);
            });
        }, 3000);
    };

    Table.prototype.Destroy = function () {
        if (Object.keys(this.players).length == 0) {
            this.channel.destroy();
            delete server.rooms[this.tableid];
        }
    };

    Table.prototype.AllPlayerCheck = function (f) {
        for (var uid in this.players) {
            var pl = this.players[uid];
            if (!f(pl)) {
                return false;
            }
        }
        return true;
    };

    Table.prototype.FindAllPlayer = function (f) {
        var rtn = [];
        for (var uid in this.players) {
            var pl = this.players[uid];
            if (f(pl)) {
                rtn.push(pl);
            }
        }
        return rtn;
    };

    Table.prototype.getPlayer = function (uid) {
        return this.players[uid];
    };

    Table.prototype.PlayerPtys = function (func) {
        var players = {};
        for (var uid in this.players) {
            var pl = this.players[uid];
            players[uid] = func(pl);
        }
        return players;
    };

    Table.prototype.collectPlayer = function () {
        var fds = arguments;
        var players = {};
        for (var uid in this.players) {
            var pl = this.players[uid];
            var info = {};
            for (var i = 0; i < fds.length; i++) {
                info[fds[i]] = pl[fds[i]];
            }
            players[pl.uid] = info;
        }
        return players;
    };

    Table.prototype.CheckPlayerCount = function (f) {
        var rtn = 0;
        for (var uid in this.players) {
            if (f(this.players[uid])) {
                rtn++;
            }
        }
        return rtn;
    };

    Table.prototype.AllPlayerRun = function (f) {
        for (var uid in this.players) {
            f(this.players[uid]);
        }
    };

    Table.prototype.AllWatcherRun = function(f) {
        for (var uid in this.watchers) {
            f(this.watchers[uid]);
        }
    };

    Table.prototype.PlayerCount = function () {
        return Object.keys(this.players).length;
    };

    Table.prototype.SetTimer = function () {
        if (!this.delayTimer) this.delayTimer = {};
        var delayTimer = this.delayTimer;
        if (delayTimer.timerId) {
            clearTimeout(delayTimer.timerId);
            delayTimer.timerId = null;
        }
        delayTimer.args = Array.prototype.slice.call(arguments);
        delayTimer.next = -2;
        function timerFunc() {
            if (delayTimer.next >= 0) delayTimer.args[delayTimer.next + 1]();
            if (delayTimer.args.length == 0) return;
            delayTimer.next += 2;
            if (delayTimer.next < delayTimer.args.length) delayTimer.timerId = setTimeout(timerFunc, delayTimer.args[delayTimer.next]);
            else delayTimer.timerId = null;
        }

        timerFunc();
    };

    Table.prototype.AddWatcher = function(pinfo, msg, callback) {
        if (this.watchers[pinfo.uid]) {
            this.WatchReconnect(this.watchers[pinfo.uid], pinfo.fid);
            callback({code: 0, message: 'SUCCESS'});
            return;
        }
        callback({code: 0, message: 'SUCCESS'});
        var newPly = new Player(pinfo);
        this.watchers[pinfo.uid] = newPly;
        server.watchers[pinfo.uid] = newPly;
        newPly.table = this;
        newPly.init();
        newPly.watching = true;
        this.channel.add(newPly.uid, newPly.fid);
        if (this.tData.watchingUids.indexOf(pinfo.uid) < 0) {
            this.tData.watchingUids.push(pinfo.uid);
        }

        newPly.notify('initSceneData', this.initSceneData());
    };


    Table.prototype.AddCheck = function (pinfo, msg, callback) {
        pinfo.clubId = 0;
        pinfo.fanshen = 0;
        delete pinfo.photo;
        delete pinfo.photo2;
        delete pinfo.field;
        delete pinfo.jipaiqi1;
        delete pinfo.jipaiqi2;
        delete pinfo.doubleCard;
        if (!this.CanAddPlayer(pinfo)) {
            return callback({ code: -1, message: '房间已满员' });
        }

        //牛牛处理操作
        if (this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) {
            if (this.createParams.antiLastJoin == true && this.tData.roundNum == 1) {
                return callback({ code: -1, message: '游戏禁止最后一局加入游戏' });
            }

            if (this.createParams.antiCenterJoin == true && this.tData.gameRuning == true) {
                return callback({ code: -1, message: '游戏禁止中途加入' });
            }
        }


        if (this.createParams.gps && this.createParams.maxPlayer > 2) {
            if (!msg.location) {
                return callback({ code: -2, message: '该房间设置了防作弊，由于未获取到您的位置信息，无法进入' });
            }
        }

        if (this.createParams.antiCheat && this.createParams.maxPlayer > 2) {
            if (this.createParams.antiCheat.toString().indexOf('1') > -1) {
                if (!msg.location) {
                    return callback({ code: -2, message: '该房间设置了防作弊，由于未获取到您的位置信息，无法进入' });
                }
            }
            if (this.createParams.antiCheat.toString().indexOf('2') > -1) {
                for (var uid in this.players) {
                    if (pinfo.remoteIP == this.players[uid].info.remoteIP) {
                        return callback({ code: -2, message: '该房间设置了防作弊，检测到您与其中玩家(' + unescape(this.players[uid].info.nickname) + ')IP相同，无法进入' });
                    }
                }
            }
            if (this.createParams.antiCheat.toString().indexOf('3') > -1) {
                for (var uid in this.players) {
                    if (utils.distance(pinfo.location.longitude, pinfo.location.latitude, this.players[uid].info.location.longitude, this.players[uid].info.location.latitude) < 150) {
                        return callback({ code: -1, message: '该房间设置了防作弊，检测到您与其中玩家(' + unescape(this.players[uid].info.nickname) + ')距离小于50米，无法进入' });
                    }
                }
            }
        }

        if (this.createParams.payWay == 1 && pinfo.money - Math.abs(pinfo.lockMoney) < this.createParams.money + this.createParams.voice) {
            return callback({ code: -4, message: '该房为AA房，由于您的元宝不足，无法进入，去购买' });
        }
        if (this.createParams.payWay == 2 && pinfo.money - Math.abs(pinfo.lockMoney) < this.createParams.money + this.createParams.voice) {
            return callback({ code: -4, message: '该房为大赢家付费，由于您的元宝不足，无法进入，去购买' });
        }
        if (!this.createParams.clubId) {
            this.AddPlayer(pinfo, msg, callback);
            return;
        }

        var table = this;
        var groupMutex = [];
        async.series([
            function (_callback) {
                mysqlSlave.execute('SELECT roomNum FROM tb_user_online WHERE userId=? AND roomNum IS NOT NULL', [pinfo.uid], (err, results) => {
                    if (err) {
                        return _callback();
                    }
                    if (!results || results.length == 0) {
                        return _callback();
                    }

                    var tmpTable = server.rooms[results[0].roomNum];
                    if (tmpTable) {
                        var pLen = tmpTable.players[pinfo.uid].length;
                        if (pLen == 1) {
                            tmpTable.dissolveWay = -1;
                            tmpTable.tData.dissolveWay = -1;
                            tmpTable.createParams.dissolveWay = -1;
                            tmpTable.AllPlayerRun(pl => { pl.mjdesc.push('系统于' + utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss') + '解散房间') });
                            tmpTable.dissolve(5);
                        } else if (pLen > 1) {
                            return _callback('已在游戏房间中，请大退再进...');
                        }
                    }
                    return _callback();
                })
            },
            function (_callback) {
                mysqlSlave.execute('SELECT isAuth FROM tb_user WHERE id=?', [pinfo.uid], (err, results) => {
                    if (err) {
                        return _callback();
                    }
                    if (!results || results.length == 0) {
                        return _callback();
                    }
                    var resu = results[0];
                    pinfo.authVal = Number(resu.isAuth);
                    pinfo.isAuth = Number(resu.isAuth) > 0;
                    //pinfo.isAuth = Number(resu.isAuth) == 1;
                    _callback();
                })
            },
            function (_callback) {
                //俱乐部
                if (!table.createParams.clubId) {
                    return _callback();
                }
                //tb_club_user.tLossScore+tb_club_user.tWinScore+tb_club_user.balanceVal
                var sql = `
                    SELECT
                        c.id clubId,
                        c.useClose,
                        c.dailyCloseTime,
                        cu.id checkJoin,
                        cu.roleId,
                        cu.happyBean,
                        cu.status checkStatus,
                        cu.juniorStatus,
                        c.clubHideStatus,
                        cum.userId1,
                        cum.userId2,
                        c.type,
                        cu.parentPartnerUserId,
                        cu.parentPartnerTree,
                        MOD(cu.balanceVal, 1000) balanceVal
                    FROM tb_club c
                        LEFT JOIN tb_club_user cu ON cu.clubId = c.id AND cu.userId = ?
                        LEFT JOIN tb_club_user_mutex cum ON cum.clubId = c.id AND (cum.userId1 = ? OR cum.userId2 = ?)
                    WHERE
                        c.id = ?
                `;
                var params = [pinfo.uid, pinfo.uid, pinfo.uid, table.createParams.clubId];
                mysqlUtil.execute(sql, params, function (err, results) {
                    if (err) {
                        logger.error(err);
                        return _callback('服务器开小差了，请稍后再试');
                    }
                    if (!results || results.length == 0) {
                        return _callback('该房间为亲友圈房间，仅成员可加入');
                    }
                    var resu = results[0];
                    table.tData.clubType = resu.type;
                    if (resu.clubHideStatus == 1) {
                        return _callback('该亲友圈已设置为隐藏状态，请到联盟中加入房间');
                    }
                    if (resu.checkStatus > 1 || resu.juniorStatus > 1) {
                        return _callback('您已禁止加入房间，如有疑问，请联系会长、管理员或上级');
                    }
                    if (!resu.checkJoin) {
                        return _callback('该房间为亲友圈房间，仅成员可加入');
                    }
                    if (resu.checkStatus == 0) {
                        return _callback('该房间为亲友圈房间，您的申请尚未通过，无法加入');
                    }
                    if (!('isAntiAddictionDissolveLimit' in table.createParams) || table.createParams.isAntiAddictionDissolveLimit == null) {
                        return _callback('亲友圈开启赛分模式但该桌子未配置，请联系管理员进行设置');
                    }
                    if (table.createParams.antiAddictionLimitScore > resu.happyBean) {
                        return _callback('赛分不足，请联系上级上分');
                    }
                    if (resu.useClose) {
                        var closeTimeArr = resu.dailyCloseTime.split('/');
                        for (var i = 0; i < closeTimeArr.length; i++) {
                            closeTimeArr[i] = parseInt(closeTimeArr[i]);
                        }
                        var closeStartTime = new Date(utils.dateFormat(new Date(), 'yyyy-MM-dd ' + closeTimeArr[0] + ':' + closeTimeArr[1] + ':00')).getTime();
                        var closeEndTime = new Date(utils.dateFormat(new Date(), 'yyyy-MM-dd ' + closeTimeArr[2] + ':' + closeTimeArr[3] + ':00')).getTime();
                        if (closeStartTime > closeEndTime) {
                            closeEndTime += 60 * 60 * 24 * 1000;
                        }
                        if (Date.now() > closeStartTime && Date.now() < closeEndTime) {
                            return _callback('打烊时间，无法进入房间');
                        }
                    }

                    pinfo.clubId = resu.clubId;
                    pinfo.roleId = resu.roleId;
                    pinfo.happyBean = resu.happyBean;
                    pinfo.parentPartnerUserId = resu.parentPartnerUserId;
                    pinfo.parentPartnerTree = resu.parentPartnerTree;
                    pinfo.balanceVal = resu.balanceVal;

                    var mutexList = [];
                    for (var i = 0; i < results.length; i++) {
                        if (results[i].userId1 && results[i].userId1 == pinfo.uid) mutexList.push(results[i].userId2);
                        if (results[i].userId2 && results[i].userId2 == pinfo.uid) mutexList.push(results[i].userId1);
                    }

                    groupMutex = [];
                    for (var uid in table.players) {
                        var tmpPlayer = table.players[uid];
                        if (tmpPlayer) {
                            groupMutex = groupMutex.concat(tmpPlayer.info.parentPartnerTree.split(","));
                            if (tmpPlayer.info.roleId == 2) {
                                groupMutex.push(uid);
                            }
                        }

                        if (utils.isContains(mutexList, uid)) {
                            return _callback('由于您与该房间内的“' + uid + '”存在互斥关系，不能进入该房间，如有疑问，请联系会长或管理员');
                        }
                    }

                    _callback();
                });
            },
            function (_callback) { //组互斥判断
                if (groupMutex.length < 2) {
                    return _callback();
                }
                let uidArr = pinfo.parentPartnerTree.split(",");
                if (pinfo.roleId == 2) {
                    uidArr.push(pinfo.uid);
                }

                if (uidArr.length < 2) {
                    return _callback();
                }
                let sql = "SELECT COUNT(1) mutexNum FROM tb_club_user_mutex WHERE clubId=? AND mutexType>0 AND ((userId1 IN (?) AND userId2 IN (?)) OR (userId1 IN (?) AND userId2 IN (?))) ";
                let params = [pinfo.clubId, uidArr, groupMutex, groupMutex, uidArr];
                mysqlUtil.execute(sql, params, function (err, results) {
                    if (err) {
                        logger.error(err);
                        return _callback('服务器开小差了，请稍后再试');
                    }
                    if (!results || results.length == 0) {
                        return _callback();
                    }

                    if (results[0].mutexNum == 0) {
                        return _callback();
                    }

                    return _callback('加入失败，存在组互斥关系');
                });
            },
            function (_callback) {
                if (pinfo.roleId == 3 || pinfo.balanceVal) {
                    return _callback();
                }
                let uidArr = pinfo.parentPartnerTree.split(",");
                let sql = "SELECT MOD(balanceVal, 1000) balanceVal FROM tb_club_user WHERE clubId=? AND userId IN (?) ORDER BY FIELD(userId, ?)";
                let params = [pinfo.clubId, uidArr, uidArr];
                mysqlUtil.execute(sql, params, function (err, results) {
                    if (err) {
                        logger.error(err);
                        return _callback('服务器开小差了，请稍后再试');
                    }
                    if (!results || results.length == 0) {
                        return _callback();
                    }
                    for (let i = results.length - 1; i >= 0; i--) {
                        if (results[i].balanceVal) {
                            pinfo.balanceVal = results[i].balanceVal;
                            break;
                        }
                    }
                    _callback();
                });
            },
            function (_callback) { // 团队预警-禁玩
                if (!table.createParams.clubId || pinfo.roleId == 3) {
                    return _callback();
                }
                let tmpTree = pinfo.parentPartnerTree;
                if (utils.isContains([2, 10], pinfo.roleId)) {
                    tmpTree = tmpTree + ',' + pinfo.uid;
                }
                var sql = `SELECT SUM(isForbid) isForbid FROM tb_club_user WHERE clubId=? AND userId IN (` + tmpTree + `)`;
                var params = [table.createParams.clubId];
                mysqlUtil.execute(sql, params, function (err, results) {
                    if (err) {
                        return _callback();
                    }
                    if (!results || results.length == 0) {
                        return _callback();
                    }
                    if (results[0].isForbid > 0) {
                        return _callback('团队积分不足，无法进入游戏，请联系上级或管理员');
                    }
                    _callback();
                })
            },
            function (_callback) { // RPC到联盟/亲友圈检查加入房间
                if (!table.createParams.clubId) {
                    return _callback();
                }

                var playerIds = [];
                for (var uid in table.players) {
                    playerIds.push(parseInt(uid));
                }
                var extra = {
                    userId: pinfo.uid,
                    createParams: {
                        ruleId: table.createParams.ruleId,
                        gameType: table.createParams.gameType,
                        players: playerIds,
                        antiCheat: table.createParams.antiCheat,
                        maxPlayer: table.createParams.maxPlayer,
                    }
                }

                //防沉迷
                if (utils.isStrNotEmpty(table.createParams.isAntiAddictionLimit)) {
                    extra.createParams.isAntiAddictionLimit = table.createParams.isAntiAddictionLimit;
                }
                if (utils.isStrNotEmpty(table.createParams.antiAddictionLimitScore)) {
                    extra.createParams.antiAddictionLimitScore = table.createParams.antiAddictionLimitScore;
                }
                if (utils.isStrNotEmpty(table.createParams.isAntiAddictionDissolveLimit)) {
                    extra.createParams.isAntiAddictionDissolveLimit = table.createParams.isAntiAddictionDissolveLimit;
                }
                if (utils.isStrNotEmpty(table.createParams.commissionBottomScore)) {
                    extra.createParams.commissionBottomScore = table.createParams.commissionBottomScore;
                }
                if (utils.isStrNotEmpty(table.createParams.isFixRate)) {
                    extra.createParams.isFixRate = table.createParams.isFixRate;
                }
                if (utils.isStrNotEmpty(table.createParams.commissionRate)) {
                    extra.createParams.commissionRate = table.createParams.commissionRate;
                }
                if (utils.isStrNotEmpty(table.createParams.xiPaiLimitScore)) {
                    extra.createParams.xiPaiLimitScore = table.createParams.xiPaiLimitScore;
                }

                if (table.createParams.clubId) {
                    app.rpc.pkclub.Rpc.checkJoinGame(pkclubDispatcher.dispatch(table.createParams.clubId), table.createParams.clubId, extra, function (err, happybean) {
                        if (err) {
                            return _callback(err);
                        }
                        logger.debug("检测加入游戏房间回调:", happybean);
                        pinfo.happybean = happybean;
                        _callback();
                    });
                }
            }
        ], function (err) {
            if (err) {
                return callback({ code: -1, message: err });
            }
            table.AddPlayer(pinfo, msg, callback);
        });
    };

    Table.prototype.AddPlayer = function (pinfo, msg, callback) {
        if (!this.CanAddPlayer(pinfo)) {
            return callback({ code: -1, message: '房间已满员' });
        }

        if (this.players[pinfo.uid]) {
            this.Reconnect(this.players[pinfo.uid], pinfo.fid);
            return callback({ code: 0, message: 'SUCCESS' });
        }

        callback({ code: 0, message: 'SUCCESS' });
        this.players[pinfo.uid] = server.players[pinfo.uid] = new Player(pinfo);

        //从旁观里移除
        this.RemoveWatcher(pinfo.uid);

        //添加权限字段
        this.players[pinfo.uid].isAuth = pinfo.isAuth || false;
        this.players[pinfo.uid].authVal = pinfo.authVal || 0;
        this.players[pinfo.uid].table = this;
        this.players[pinfo.uid].getCard = [];

        this.initPlayer(this.players[pinfo.uid], msg);
        this.channel.add(this.players[pinfo.uid].uid, this.players[pinfo.uid].fid);
        this.players[pinfo.uid].notify('initSceneData', this.initSceneData(this.players[pinfo.uid]));
        var rpcPara = { playerCount: this.PlayerCount() };
        var roomDate = utils.clone(this.createParams);
        roomDate.createTime = this.createTime;
        var index = 0;
        roomDate.players = [];
        for (var uid in this.players) {
            index++;
            var currP = this.players[uid].info;
            var nickname = currP.nickname;
            var headimgurl = currP.headimgurl;
            var happyBean = currP.happyBean;
            if (this.createParams.isBuDaGang) {
                nickname = '玩家' + index;
                headimgurl = 'http://yl.huhougame.com/vir/168168.jpg';
            }
            roomDate.players.push({ uid: currP.uid, nickname: nickname, headimgurl: headimgurl, offline: !this.players[uid].onLine, happyBean: happyBean, type: currP.type });
        }
        redisUtil.client().setex('ROOM_' + this.tableid, 1800, JSON.stringify(roomDate));

        //牛牛处理操作，牛牛满足2个人以上就可以开始游戏
        var StartPlayerCount = 2;
        if (this.pGameCode.StartPlayerCount) {
            StartPlayerCount = this.pGameCode.StartPlayerCount(this);
        }

        if ((this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.PlayerCount() >= StartPlayerCount && !this.tData.gameRuning) {
            this.startGame();
            rpcPara.tableState = 'ongoing';
        }

        if ((this.tData.gameType != GAME_TYPE.NIU_NIU && this.tData.gameType != GAME_TYPE.ZHA_JIN_HUA && this.tData.gameType != GAME_TYPE.LAO_YAN_CAI) && this.IsFull()) {
            this.startGame();
            this.jpushMessage(1, config.common.serverName, '你加入的房间已经满人了，点击返回牌局>>>');
            rpcPara.tableState = 'ongoing';
        }
        if (this.createParams.clubId) {
            this.lastPlLeaveTime = roomDate.players.length == 0 ? Date.now() : -1;
            //app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, this.IsFull() ? 'start' : 'players', {ruleId: this.createParams.ruleId, roomNum: this.tableid, players: roomDate.players}, emptyFunction);
            if (this.IsFull()) {
                app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'start', { ruleId: this.createParams.ruleId, roomNum: this.tableid, players: roomDate.players }, emptyFunction);

                //机器人处理  真实玩家进桌自动准备
                var currTable = this;
                for (var uid in this.players) {
                    let p = this.players[uid];
                    if (p.info.type == 4) {
                        if (p.robotReadyTime != null) {
                            clearTimeout(p.robotReadyTime);
                            p.robotReadyTime = null;
                        }
                        p.robotReadyTime = setTimeout(function () {
                            if (currTable.pGameCode.mjPassCard) {
                                currTable.pGameCode.mjPassCard(p, { eatFlag: 0 }, null, emptyFunction, currTable);
                            } else if (currTable.pGameCode.pkPassCard) {
                                currTable.pGameCode.pkPassCard(p, { eatFlag: 0 }, null, emptyFunction, currTable);
                            }
                        }, Math.floor(Math.random(1000) + 2001)); //机器人处理 自动准备 随机1~2秒
                    }
                }
            } else {
                app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'players', { ruleId: this.createParams.ruleId, roomNum: this.tableid, players: roomDate.players }, emptyFunction);
            }
            return;
        }
        app.rpc.pkplayer.Rpc.refreshUserTable(app.GetServerById('pkplayer', this.tData.owner).id, this.tData.owner, this.tableid, rpcPara, emptyFunction);
    };
    Table.prototype.jpushMessage = function(type, title, content) {
        if (!type) type = 1;
        if (!title) title = config.common.serverName;
        if (!content) content = '你加入的房间已经满人了，点击返回牌局>>>';
        var uids = [];
        for (var uid in this.players) {
            if (!this.players[uid].onLine) {
                uids.push(uid);
            }
        }
        jpushUtil.send({uids: uids, type: type, title: title, content: content});
    };
    Table.prototype.RemovePlayer = function (uid) {
        var tData = this.tData; // 将tData定义移到函数开始处，确保整个函数都能访问
        if (!tData) {
            // 如果tData不存在，只进行基本的清理操作
            delete this.players[uid];
            delete server.players[uid];
            return true;
        }
        var pl = this.players[uid];
        if (pl) {
            pl.clearAllTimer();
            this.channel.leave(pl.uid, pl.fid);
            //牛牛处理操作
            if (tData.rungingUids && tData.rungingUids.indexOf(pl.uid) >= 0) {
                tData.rungingUids.splice(tData.rungingUids.indexOf(pl.uid), 1);
            }

            if (tData.tState == TableState.waitJoin || tData.tState == TableState.waitReady) {
                if (tData.uidsQueue) {
                    var p = tData.uidsQueue.indexOf(pl.uid);
                    if (p >= 0) {
                        tData.uidsQueue.splice(p, 1);
                    }
                }
                if (tData.uids) {
                    var idx = tData.uids.indexOf(pl.uid);
                    if (idx >= 0) {
                        tData.uids[idx] = 0;
                        // 打筒子 霸炸弹4人分组
                        if ((tData.gameType == GAME_TYPE.DA_TONG_ZI_SHAO_YANG && tData.maxPlayer == 4) || (tData.gameType == GAME_TYPE.LONG_HUI_BA_ZHA_DAN && tData.isDivideTeam) || (tData.gameType == GAME_TYPE.DIAN_TUO)) {
                            if (tData.inTeamUids) {
                                var idx2 = tData.inTeamUids.indexOf(pl.uid);
                                if (idx2 >= 0) {
                                    tData.inTeamUids[idx2] = 0;
                                }
                            }
                        }
                        this.NotifyAll("removePlayer", { uid: pl.uid, mjState: pl.mjState, tData: tData });
                    }
                }
            }
            delete pl.table;
        }
        delete this.players[uid];
        delete server.players[uid];
        if (this.PlayerCount() == 0 && this.tData.roundNum == -2) {
            this.tData.roundNum = -3;
            this.Destroy();
        }

        //牛牛处理操作
        //游戏未开始之前，有人退出，如果房间中的人数大于等于2，而且全部准备了就直接开始游戏（牛牛）
        if ((tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.tData.roundNum == this.tData.roundAll) {
            var playerReadyCount = 0;
            if (tData.rungingUids && tData.rungingUids.length) {
                for (var i = 0; i < tData.rungingUids.length; i++) {
                    playerReadyCount++;
                }
            }

            if (playerReadyCount >= 2) {
                this.tData.hasReadyBtn = false;
                this.startGame();
            }
        }
        return true;
    };

    Table.prototype.RemoveWatcher = function(uid) {
        var pl = this.watchers[uid];
        if (pl) {
            this.channel.leave(pl.uid, pl.fid);
            delete pl.table;
        }

        var tData = this.tData;
        if (tData.watchingUids.indexOf(uid) >= 0) {
            tData.watchingUids.splice(tData.watchingUids.indexOf(uid), 1);
        }

        delete this.watchers[uid];
        delete server.watchers[uid];
    }


    Table.prototype.IsFull = function () {
        return this.PlayerCount() >= this.tData.maxPlayer;
    };

    Table.prototype.NotifyAll = function (cmd, msg) {
        if (cmd == 'roundEnd') {
            msg.serverTime = Date.now();
        }

        var msgStr = JSON.stringify(msg);
        logger.info('[通知全部][' + cmd + ']:', msgStr);
        this.channel.pushMessage(cmd, { enmsg: utils.encrypt(msgStr) });
    };

    Table.prototype.RoomEnd = function (msg) {
        var tData = this.tData;
        tData.roundNumPre = tData.roundNum;
        if (tData.roundNum == tData.roundAll && !tData.hePai && !tData.hasPay) {
            if (this.createParams.payWay == 0) {
                this.tData.alreadyUnlockMoney = true;
                var lockMoneyNum = -this.createParams.money;
                if (this.createParams.realtimeVoice) {
                    lockMoneyNum -= this.createParams.voice;
                }
                app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', this.createParams.owner).id, this.createParams.owner, { $inc: { lockMoney: lockMoneyNum } }, true, emptyFunction);
            }
        }
        if (tData.tState == TableState.waitPut || tData.tState == TableState.waitEat || tData.tState == TableState.waitCard || tData.tState == TableState.waitLong || tData.tState == TableState.waitBao || tData.tState == TableState.waitBaoTing) {
            if (tData.hePai) {
                tData.roundNumPre = tData.roundNum - 1;
                if (tData.roundNumPre < 0) {
                    tData.roundNumPre = 0;
                }
                this.pGameCode.EndGame(this, null, null, true);
            } else {
                tData.roundNum = 1;
                this.pGameCode.EndGame(this, null, true, false);
            }
        } else {
            this.pGameCode.EndRoom(this, msg);
        }
    };

    //断线
    Table.prototype.Disconnect = function (pl) {
        this.channel.leave(pl.uid, pl.fid);
        pl.fid = null;
        pl.onLine = false;
        pl.heartbeatTime = -1;
        pl.lastOffLineTime = pl.lastOffLineTime || Date.now();

        if (this.tData.extraTimeType != 2 || !this.pGameCode.checkRoundEndReconnect) {
            pl.mjState = this.tData.tState == TableState.waitReady ? TableState.waitReady : pl.mjState;
        }

        //机器人处理 pl.info.type == 4
        if (pl.info.type == 4) {
            pl.mjState = this.tData.tState == TableState.waitReady ? TableState.isReady : pl.mjState;
        }

        // pl.mjState = this.tData.tState == TableState.waitReady ? TableState.waitReady : pl.mjState;
        this.NotifyAll('onlinePlayer', { uid: pl.uid, onLine: false, mjState: pl.mjState });
        if (this.createParams.clubId) {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'onoff', { ruleId: this.createParams.ruleId, roomNum: this.tableid, userId: pl.uid, offline: true }, emptyFunction);
        }
        if (this.CanLeaveGame()) {
            if (pl.kickoutTimer) {
                clearTimeout(pl.kickoutTimer);
            }
            var minute = this.createParams.kickoutMinute || 5;
            pl.kickoutTimer = setTimeout(() => {
                if (this.CanLeaveGame()) {
                    app.rpc.pkplayer.Rpc.LeaveGame(app.GetServerById('pkplayer', pl.uid).id, {}, { local: true, uid: pl.uid, tableid: this.tableid, reason: '由于您离线时长超过' + minute + '分钟，系统已将您踢出房间(' + this.tableid + ')' }, emptyFunction);
                }
            }, minute * 60000);
        }
    };

    //旁观断线
    Table.prototype.WatchDisconnect = function(pl) {
        // this.channel.leave(pl.uid, pl.fid);
        // pl.fid = null;
        // pl.onLine = false;
        // pl.heartbeatTime = -1;
        // pl.lastOffLineTime = pl.lastOffLineTime || Date.now();
        this.RemoveWatcher(pl.uid);
    }

    //旁观重连
    Table.prototype.WatchReconnect = function(pl, nfid) {
        this.channel.leave(pl.uid, pl.fid);
        pl.fid = nfid;
        pl.onLine = true;
        pl.heartbeatTime = Date.now();
        pl.lastOffLineTime = null;
        pl.notify('initSceneData', this.initSceneData());
        this.channel.add(pl.uid, pl.fid);
    };

    //重连
    Table.prototype.Reconnect = function (pl, nfid) {
        this.channel.leave(pl.uid, pl.fid);
        pl.fid = nfid;
        pl.onLine = true;
        pl.heartbeatTime = Date.now();
        pl.lastOffLineTime = null;
        var isAutoReady = env.indexOf('-') == -1 || this.tData.extraTimeType != 2 || !this.pGameCode.checkRoundEndReconnect;
        if (isAutoReady || pl.info.type == 4) { //机器人处理 pl.info.type == 4
            pl.mjState = (this.tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish) ? TableState.isReady : pl.mjState;
        }

        //牛牛处理操作
        var StartPlayerCount = 2;
        if (this.pGameCode.StartPlayerCount) {
            StartPlayerCount = this.pGameCode.StartPlayerCount(this);
        }
        if ((this.tData.gameType === GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) && this.PlayerCount() >= StartPlayerCount) {
            if (!this.tData.gameRuning) pl.mjState = TableState.waitReady;
        }


        // pl.mjState = (this.tData.tState == TableState.roundFinish && pl.mjState == TableState.roundFinish) ? TableState.isReady : pl.mjState;
        pl.notify('initSceneData', this.initSceneData(pl));
        if (!isAutoReady) {
            this.pGameCode.checkRoundEndReconnect(this, pl);
        }
        this.NotifyAll('onlinePlayer', { uid: pl.uid, onLine: true, mjState: pl.mjState });
        this.channel.add(pl.uid, pl.fid);
        if (pl.kickoutTimer) {
            clearTimeout(pl.kickoutTimer);
            delete pl.kickoutTimer;
        }
        if (this.createParams.clubId) {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'onoff', { ruleId: this.createParams.ruleId, roomNum: this.tableid, userId: pl.uid, offline: false }, emptyFunction);
        }
        if (this.tData.uids.length < this.tData.maxPlayer) {
            return;
        }
        if (this.tData.uids.indexOf(0) >= 0 && this.tData.uids.indexOf(0) < this.tData.maxPlayer) {
            return;
        }
        if (this.pGameCode.checkIsAllReady && this.pGameCode.checkIsAllReady(this)) {
            if (this.pGameCode.isDoPiaofen && this.pGameCode.doPiaofen && this.pGameCode.isDoPiaofen(this)) {
                this.pGameCode.doPiaofen(this);
                return;
            } else if (this.pGameCode.isDoAfterReady && this.pGameCode.doAfterReady && this.pGameCode.isDoAfterReady(this)) {
                this.pGameCode.doAfterReady(this);
                return;
            }
        }
        if (this.tData.tState == TableState.waitJoin || this.tData.tState == TableState.roundFinish) {
            this.startGame();
        }
    };

    //是否可以添加玩家
    Table.prototype.CanAddPlayer = function (pl) {
        // if (pl.type == 4) { //机器人处理 机器人不坐一起
        //     for (var uid in this.players) {
        //         let p = this.players[uid];
        //         if (p.info.type == 4) {
        //             return false;
        //         }
        //     }
        // }

        var tData = this.tData;
        var uids = tData.uids;
        var result = false;
        if (tData.roundNum > -2) {
            if (uids.indexOf(pl.uid) < 0) {
                if ((uids.length == tData.maxPlayer) && (uids.indexOf(0) < 0)) {
                    result = false;
                } else {
                    result = true;
                }
            } else {
                result = true;
            }
        }
        return result;
    };

    //是否可以离开游戏
    Table.prototype.CanLeaveGame = function () {
        if (this.tData.tState == TableState.waitJoin || this.tData.tState == TableState.waitReady || this.tData.roundNum == -2) {
            return true;
        }
        return false;
    };

    //是否要选座位
    Table.prototype.canSelectDir = function () {
        var tData = this.tData;
        if (tData.areaSelectMode["jiapiao"]) { //有飘分，不能选座位
            return false;
        }
        if (tData.areaSelectMode["xuanzuo"] === false) { //选座选项
            return false;
        }
        if (utils.isContains([
            GAME_TYPE.JIN_ZHONG_MJ, GAME_TYPE.JIN_ZHONG_CAI_SHEN,
            GAME_TYPE.LING_SHI_BAN_MO, GAME_TYPE.LING_SHI_BIAN_LONG,
            GAME_TYPE.PING_YAO_KOU_DIAN, GAME_TYPE.PING_YAO_MA_JIANG,
            GAME_TYPE.JIN_ZHONG_TUI_DAO_HU, GAME_TYPE.JIE_XIU_1_DIAN_3,
            GAME_TYPE.JIE_XIU_KOU_DIAN, GAME_TYPE.SHOU_YANG_QUE_KA,
            GAME_TYPE.JIN_ZHONG_LI_SI, GAME_TYPE.JIN_ZHONG_KD,
            GAME_TYPE.JIN_ZHONG_LI_SI, GAME_TYPE.YUN_CHENG_FENG_HAO_ZI,
            GAME_TYPE.KOU_DIAN_FENG_ZUI_ZI, GAME_TYPE.XIAO_YI_KOU_DIAN,
            GAME_TYPE.LV_LIANG_MA_JIANG, GAME_TYPE.LIN_FEN_YING_SAN_ZUI,
            GAME_TYPE.LIN_FEN_YI_MEN_ZI, GAME_TYPE.HONG_TONG_WANG_PAI,
            GAME_TYPE.FEN_XI_YING_KOU, GAME_TYPE.LIN_FEN_XIANG_NING_SHUAI_JIN,
            GAME_TYPE.JI_XIAN_1928_JIA_ZHANG, GAME_TYPE.FAN_SHI_XIA_YU,
            GAME_TYPE.DAI_XIAN_MA_JIANG, GAME_TYPE.WU_TAI_KOU_DIAN,
            GAME_TYPE.JIN_ZHONG_GUAI_SAN_JIAO, GAME_TYPE.DA_NING_SHUAI_JIN,
            GAME_TYPE.FEN_YANG_QUE_MEN, GAME_TYPE.DA_TONG_GUAI_SAN_JIAO,
            GAME_TYPE.JING_LE_KOU_DIAN, GAME_TYPE.LUAN_GUA_FENG,
            GAME_TYPE.JI_SHAN_NIU_YE_ZI, GAME_TYPE.YUN_CHENG_TIE_JIN,
            GAME_TYPE.JI_SHAN_NIU_YE_ZI, GAME_TYPE.HE_JIN_KUN_JIN,
            GAME_TYPE.ZHUO_HAO_ZI,
        ], this.createParams.gameType)) {
            return true;
        }
        return false;
    };

    Table.prototype.initSceneData = function (pl) {
        var players;
        if (this.pGameCode.collectPlayer) {
            players = this.pGameCode.collectPlayer(this);
        }
        else {
            players = this.collectPlayer(
                'trust',
                'eatFlag',
                'eatFlag2',         // 长沙麻将同时出两个牌时专用，
                'eatFlag3',
                'eatFlag4',
                'handCount',
                'putCount',
                'zuiUid',
                'zuiCount',
                'jiaPai',
                'qingDui',
                'info',
                'mjState',
                'firstFlower8', //如皋麻将胡，是否起手8花
                'mjpeng',
                'mjgang0',
                'mjgang1',
                'mjgang0Card',
                'mjgang1Card',
                'mjTeshuGang0',
                'mjTeshuGang1',
                'mjchi',
                'mjchiCard',
                'mjfei',
                'mjfeiCard',
                'mjput',
                'onLine',
                'huType',
                'skipHu',
                'skipPeng',
                'skipGang',
                'skipLong',
                'isQiHu',
                'delRoom',
                'delRoomHePai', //如皋长牌的和牌
                'isNew',
                'winall',
                'linkZhuang',
                'pengchigang',
                'isTing',
                'mjflower',                     //花牌
                'newSendCard',                  //新发的牌,听牌使用
                'roomStatistics',
                'zimoTotal',
                'dianpaoTotal',
                'minggangTotal',
                'angangTotal',
                'putCardAfterTing',             //听牌之后出的那张牌
                'tingIndex',                    //听牌出牌的位置
                'jiazhuNum',                     //加注
                'jiachuiNum',                    // 加锤
                'mjwei',                        //偎牌
                'mjsort',                        //提、偎、跑、碰、吃的顺序
                'wangType',
                'wangStatus',
                'long',
                'locationMsg',                  // 位置信息
                'locationApps',                 // 定位修改App
                'rate',                          // 倍率
                'qiShouHu',
                'haiDiLaoState',                 // 长沙麻将 海底捞
                'isTianting',                    // 涟水麻将，天听
                'tableMsg',
                'jiaoFen',              // 三打哈叫分
                'isPaiFen',            // 三打哈拍分
                'isAgreeTouXiang',  // 三打哈投降选择
                'limitHuPutCard',  // 耒阳字牌打后限胡的牌
                'limitHuTypeList',  // 耒阳字牌限胡类型
                'canNotPutCard',  // 耒阳字牌不能打的牌
                'canGangHand',   // 耒阳麻将 能否杠手牌标志
                'gangFourCounts',
                'mjhandFour',
                'qiang',
                'mustJiao',
                'dir',
                'lastOffLineTime',
                'piaoFen',
                'gangScore',     // 每局杠分计算
                'freeBegin',       // 自由人数投票数据
                'touzi',            //新宁麻将
                'winone',
                'jiepaoTotal',       //接炮次数
                'tPutCard',
                'isclickTing'       //安化四王麻将是否点击报听
            );
        }
        var msg = { players: players, tData: JSON.parse(JSON.stringify(this.tData)), serverNow: Date.now() };
        if (utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.LONG_HUI_BA_ZHA_DAN, GAME_TYPE.DIAN_TUO], this.createParams.gameType) && this.pGameCode.collectTeam) {
            msg.teams = this.pGameCode.collectTeam(this);
        }
        if (utils.isContains([GAME_TYPE.YUE_YANG_NIU_SHI_BIE, GAME_TYPE.ZHU_ZHOU_DA_MA_ZI], this.createParams.gameType) && this.pGameCode.collectMinPai) {
            msg.MinPai = this.pGameCode.collectMinPai(pl, this);
            if (this.pGameCode.collectTouXiang)
                msg.TouXiangData = this.pGameCode.collectTouXiang(pl, this);
        }
        if (utils.isContains([GAME_TYPE.CHONG_YANG_DA_GUN, GAME_TYPE.DA_YE_510K, GAME_TYPE.QI_CHUN_DA_GONG, GAME_TYPE.TONG_SHAN_DA_GONG, GAME_TYPE.DA_YE_DA_GONG, GAME_TYPE.WU_XUE_510K], this.createParams.gameType) &&
            this.pGameCode.collectPartnerCards) {
            msg.partnerCards = this.pGameCode.collectPartnerCards(pl, this);
        }
        // 当可多胡时部分玩家胡了，也把手牌返回给所有玩家
        if (utils.isContains(['yueyang', 'hubei', 'shaoyang', 'yueyang-test', 'hubei-test', 'shaoyang-test', 'dev-c'], env)) {
            this.AllPlayerRun((p) => {
                if (p.mjState == TableState.roundFinish && this.tData.tState != TableState.roundFinish) {
                    msg.players[p.uid].mjhand = p.mjhand;
                }
            });
        }
        // 炸金花重连手牌处理
        if (utils.isContains([GAME_TYPE.ZHA_JIN_HUA], this.createParams.gameType)) {
            this.AllPlayerRun((p) => {
                //logger.debug("重连手牌处理mjState=%d this.tData.tState=%d p.MingPai=%d", p.mjState, this.tData.tState, p.MingPai);
                if (p && p.mjState == TableState.waitJiazhu && this.tData.tState != TableState.roundFinish) {
                    msg.players[p.uid].mjhand = [0, 0, 0];
                }
            });
        }
        // 捞腌菜手牌处理
        if (utils.isContains([GAME_TYPE.LAO_YAN_CAI], this.createParams.gameType)) {
            this.AllPlayerRun((p) => {
                if (p && this.tData.tState != TableState.roundFinish) {
                    msg.players[p.uid].mjhand = [0, 0];
                }
            });
        }

        if (pl) {
            // 将剩余的牌发给玩家
            if (this.tData.jipaiqi && utils.isContains(this.tData.jipaiqi, pl.uid) && utils.isContains([GAME_TYPE.DA_YE_510K, GAME_TYPE.QI_CHUN_DA_GONG, GAME_TYPE.WU_XUE_510K], this.createPara.gameType)) {
                msg.leftCards = this.pGameCode.getLeftCards(this);
            }

            msg.players[pl.uid].mjhand = pl.mjhand;
            if (utils.isContains([GAME_TYPE.ZHA_JIN_HUA], this.createParams.gameType)) {
                if (pl && pl.mjState == TableState.waitJiazhu && this.tData.tState != TableState.roundFinish && !pl.MingPai) {
                    msg.players[pl.uid].mjhand = [0, 0, 0];
                }
            }

            msg.players[pl.uid].mjpeng4 = pl.mjpeng4;
            if (this.tData.gameType == GAME_TYPE.GUI_ZHOU_MEN_HU_XUE_LIU || this.tData.gameType == GAME_TYPE.GUI_ZHOU_GUI_YANG_ZHUO_JI || this.tData.gameType == GAME_TYPE.GUI_ZHOU_AN_LONG_MJ) { // 闷胡血流 闷胡牌
                this.AllPlayerRun(function (p) {
                    msg.players[p.uid].menHu = [];
                })
                msg.players[pl.uid].menHu = pl.menHu;
            }
            if (this.tData.gameType == GAME_TYPE.XIAO_GAN_KA_WU_XING || this.tData.gameType == GAME_TYPE.SUI_ZHOU_KA_WU_XING) { // 孝感以及随州卡五星
                this.AllPlayerRun(function (p) {
                    msg.players[p.uid].anKe = [];
                })
                msg.players[pl.uid].anKe = pl.anKe;
            }
            if (this.tData.gameType == GAME_TYPE.GUI_ZHOU_XUE_ZHAN_DAO_DI_MJ) {//血战到底
                this.AllPlayerRun((p) => { msg.players[p.uid].huCards = [] });
                msg.players[pl.uid].huCards = pl.huCards;
            }
            if (this.canSelectDir()) {
                if (pl.dir < 0 && (this.tData.maxPlayer == 4 || this.tData.maxPlayer == 3)) {
                    msg.players[pl.uid].mjState = TableState.waitReady;
                    pl.mjState = TableState.waitReady;
                    if (pl.selectTimeOut) {
                        clearTimeout(pl.selectTimeOut);
                        pl.selectTimeOut = null;
                    }
                    pl.selectTimeOut = setTimeout(function () {
                        app.rpc.pkplayer.Rpc.LeaveGame(app.GetServerById('pkplayer', pl.uid).id, {}, { local: true, uid: pl.uid, reason: '由于您未选座位，已被踢出房间！' }, emptyFunction);
                    }, 10 * 1000); // 倒计时离开房间
                }
            }
        }
        return msg;
    };
    /**
     * 开始游戏 发牌
     */
    Table.prototype.startGame = function () {
        //有可能在开始前要做一些玩家选择操作的时候，直接在此处理
        var tData = this.tData;
        logger.debug("====================startGame==========0000000000000000000000000========");
        // logger.debug("tData.uids",tData.uids, "tData.maxPlayer",tData.maxPlayer, "tData.roundNum", tData.roundNum)

        var StartPlayerCount = tData.maxPlayer;
        if (this.pGameCode.StartPlayerCount) {
            StartPlayerCount = this.pGameCode.StartPlayerCount(this);
        }

        //玩家人数不够，不去发送消息

        if(tData.gameType != GAME_TYPE.NIU_NIU && tData.gameType != GAME_TYPE.ZHA_JIN_HUA && this.tData.gameType != GAME_TYPE.LAO_YAN_CAI) {
            if (tData.uids.length < tData.maxPlayer) {
                return;
            }
            //删除玩家之后uids里边重置为0
            if (tData.uids.indexOf(0) >= 0 && tData.uids.indexOf(0) < tData.maxPlayer) {
                return;
            }
        }
        if (tData.uids.length < StartPlayerCount) {
            return;
        }
        // //删除玩家之后uids里边重置为0
        // if (tData.uids.indexOf(0) >= 0 && tData.uids.indexOf(0) < StartPlayerCount) {
        //     return;
        // }

        //牛牛处理操作
        if (tData.gameType == GAME_TYPE.NIU_NIU) {
            if (!tData.gameRuning && tData.roundNum == tData.roundAll && !tData.freeBeginStart && this.pGameCode.isBtnReady && this.pGameCode.isBtnReady(tData)) {
                if (!tData.hasReadyBtn) { // 准备流程，只会进来一次
                    tData.hasReadyBtn = true;
                    tData.tState = TableState.waitReady;
                    this.AllPlayerRun(function (p) {
                        p.mjState = TableState.waitReady;
                    });

                    this.NotifyAll("waitReady", {});
                }
            } else {
                this.tData.gameRuning = true;
                if (this.mjlog.length == 0) {
                    cloneDataAndPush(this.mjlog, "initSceneData", this.initSceneData());
                    //logger.debug("initSceneData=====", JSON.stringify(this.initSceneData()));
                    if (this.trustWholeLog && this.trustWholeLog.length > 0) {
                        var cmdLog = this.trustWholeLog.pop();
                        cloneDataAndPush(this.mjlog, cmdLog.cmd, cmdLog.data);
                    }
                }

                logger.debug("++++++++++++++++++++++++++++++ zhuangType = " + this.createParams.zhuangType);
                if (this.createParams.zhuangType == 0)               //轮庄模式
                {
                    this.pGameCode.startTrunBank(tData, this);
                }
                else if (this.createParams.zhuangType == 2)         //自由抢庄
                {
                    this.pGameCode.startCallBank(tData, this);
                }
                else if (this.createParams.zhuangType == 3 || this.createParams.zhuangType == 5)          //明牌倍数抢庄与明牌普通抢庄
                {
                    this.pGameCode.startMingCallBank(tData, this);
                }
                else if (this.createParams.zhuangType == 4)          //房主坐庄
                {
                    this.pGameCode.startOwnerBank(tData, this);
                }
                else {
                    this.pGameCode.countZhuang(tData, this);
                    tData.tState = TableState.waitJiazhu;
                    var tuiZhuUid = [];
                    for (var i = 0; i < tData.rungingUids.length; i++) {
                        var pl = this.players[tData.rungingUids[i]];
                        pl.mjState = TableState.waitJiazhu;
                        if (pl.canTuiZhu) {
                            tuiZhuUid.push(pl.uid);
                        }
                    }

                    var jiaZhuMsg = {};
                    jiaZhuMsg.tuiZhuUid = JSON.parse(JSON.stringify(tuiZhuUid));
                    jiaZhuMsg.zhuang = tData.zhuang;
                    jiaZhuMsg.rungingUids = JSON.parse(JSON.stringify(tData.rungingUids));
                    this.NotifyAll("waitJiazhu", jiaZhuMsg);
                    this.cancelTrustWholeAction();
                    this.trustWhole();
                    //this.pGameCode.checkTimer(this);
                }
            }
        }

        if (tData.gameType === GAME_TYPE.ZHA_JIN_HUA) {
            if (this.PlayerCount() < 2) return;

            if (tData.roundNum > 0 && this.PlayerCount() <= tData.maxPlayer && !tData.gameRuning) {
                logger.debug("ZHA_JIN_HUA tData.tState = %d tData.hasReadyBtn = %d ", tData.tState, tData.hasReadyBtn);
                tData.hasReadyBtn = true;
                tData.tState = TableState.waitReady;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitReady;
                });
                if (this.createParams.isNeedReady == 0) {
                    this.AllPlayerRun((pl) => {
                        if (this.pGameCode.mjPassCard) {
                            this.pGameCode.mjPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                        }
                        else if (this.pGameCode.pkPassCard) {
                            this.pGameCode.pkPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                        }
                    });
                    return;
                }
                this.NotifyAll("waitReady", {});
            }
        }

        if (tData.gameType === GAME_TYPE.LAO_YAN_CAI) {
            if (this.PlayerCount() < this.pGameCode.StartPlayerCount(this)) return; 
            
            if (!tData.gameRuning && tData.roundNum == tData.roundAll && !tData.freeBeginStart && this.pGameCode.isBtnReady && this.pGameCode.isBtnReady(tData)) {
                if (!tData.hasReadyBtn) { // 准备流程，只会进来一次
                     tData.tState = TableState.waitReady;
                     this.AllPlayerRun(function(p) {
                         p.mjState = TableState.waitReady;
                     });

                    this.NotifyAll("waitReady", {});
                }
            }
            else
            {
                this.tData.gameRuning = true;
                if (this.mjlog.length == 0) {
                    cloneDataAndPush(this.mjlog, "initSceneData", this.initSceneData());
                    //logger.debug("initSceneData=====", JSON.stringify(this.initSceneData()));
                    if(this.trustWholeLog && this.trustWholeLog.length > 0)
                    {
                        var cmdLog = this.trustWholeLog.pop();
                        cloneDataAndPush(this.mjlog, cmdLog.cmd, cmdLog.data);
                    }
                }

                logger.debug("++++++++++++++++++++++++++++++ zhuangType = " + this.createParams.zhuangType);
                if (this.createParams.zhuangType == 0)              //自由抢庄
                {
                    this.pGameCode.startMingCallBank(tData, this);
                }
                else if(this.createParams.zhuangType == 1)          //房主坐庄
                {
                    this.pGameCode.startOwnerBank(tData, this);
                }
                else if(this.createParams.zhuangType == 2)          //轮庄模式
                {
                    this.pGameCode.startTrunBank(tData, this);
                }
                else
                {
                    this.pGameCode.countZhuang(tData, this);
                    tData.tState = TableState.waitJiazhu;
    
                    var jiaZhuMsg = {};
                    jiaZhuMsg.zhuang = tData.zhuang;
                    jiaZhuMsg.rungingUids = JSON.parse(JSON.stringify(tData.rungingUids));
                    this.NotifyAll("waitJiazhu", jiaZhuMsg);
                    this.cancelTrustWholeAction();
                    this.trustWhole();
                }
            }
        }

        if (tData.roundNum > 0 && this.PlayerCount() == tData.maxPlayer && this.AllPlayerCheck((pl) => { return tData.uids.indexOf(pl.uid) < 0 || pl.mjState == TableState.isReady })) {
            this.pGameCode.countZhuang(tData, this);
            var zhuangPl = this.players[tData.uids[tData.zhuang]];
            // 含有准备过程isBtnReady的房间第一局
            if (tData.roundNum == tData.roundAll && !tData.freeBeginStart && this.pGameCode.isBtnReady && this.pGameCode.isBtnReady(tData)) {
                if (!tData.hasReadyBtn) { // 准备流程，只会进来一次
                    tData.hasReadyBtn = true;
                    tData.tState = TableState.waitReady;
                    this.AllPlayerRun(function (p) {
                        p.mjState = TableState.waitReady;
                    });
                    if (this.createParams.isNeedReady == 0 && !utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.LONG_HUI_BA_ZHA_DAN], this.createParams.gameType)) {
                        this.AllPlayerRun((pl) => {
                            if (this.pGameCode.mjPassCard) {
                                this.pGameCode.mjPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                            }
                            else if (this.pGameCode.pkPassCard) {
                                this.pGameCode.pkPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                            }
                        });
                        return;
                    }
                    this.NotifyAll("waitReady", {});
                } else if (this.pGameCode.checkJiaZhu && this.pGameCode.checkJiaZhu(this)) {
                    tData.tState = TableState.waitJiazhu;
                    this.AllPlayerRun(function (p) {
                        p.mjState = TableState.waitJiazhu;
                    });
                    this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang });
                    if ((tData.gameType == GAME_TYPE.ML_HONGZHONG || tData.gameType == GAME_TYPE.ZHAO_TONG_MJ) && (tData.areaSelectMode["jiapiao"] || tData.areaSelectMode["piaofen"] >= 4)) {
                        this.cancelTrustWholeAction();
                        this.trustWhole();
                    }
                }
            } else if ((tData.gameType == GAME_TYPE.DONG_HAI && tData.areaSelectMode["isJiaZhu"])
                || tData.gameType == GAME_TYPE.NIU_NIU
                || (tData.gameType == GAME_TYPE.SU_QIAN && tData.areaSelectMode["lazhuangFen"] && zhuangPl.linkZhuang)
                || (tData.gameType == GAME_TYPE.XU_ZHOU && tData.areaSelectMode["isJiaZhu"])
                || (tData.gameType == GAME_TYPE.HUAI_AN_CC && tData.areaSelectMode["lazhuang"] && zhuangPl.linkZhuang)
                || (tData.gameType == GAME_TYPE.HZ_TUI_DAO_HU && tData.areaSelectMode["lazhuang"] && zhuangPl.linkZhuang)
                || (tData.gameType == GAME_TYPE.NTHZ && tData.areaSelectMode["piaofen"] >= 1 && tData.isValidTable)
                || tData.gameType == GAME_TYPE.RU_GAO && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.RU_GAO_ER && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.BAI_PU_LIN_ZI && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.RU_GAO_SHUANG_JIANG && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.RU_DONG_SHUANG_JIANG && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.HAI_AN_MJ && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.HAI_AN_BAI_DA && tData.areaSelectMode["maizhuang"]
                || (this.pGameCode.checkJiaZhu && this.pGameCode.checkJiaZhu(this))
                || (tData.gameType == GAME_TYPE.DAO_ZHOU_MJ && tData.areaSelectMode.kepiao)
                || tData.gameType == GAME_TYPE.XU_ZHOU_PEI_XIAN && tData.areaSelectMode["maizhuang"]
                || tData.gameType == GAME_TYPE.TY_HONGZHONG && tData.roundNum == tData.roundAll && tData.areaSelectMode["piaoType"] == 5
                || (tData.gameType == GAME_TYPE.HENG_YANG_FANG_PAO_FA && tData.areaSelectMode.tuototuo > 0 && tData.roundNum == tData.roundAll)
                || (tData.gameType == GAME_TYPE.SHAO_YANG_FANG_PAO_FA && tData.areaSelectMode.tuototuo > 0 && tData.roundNum == tData.roundAll)
                || tData.gameType == GAME_TYPE.CHONG_YANG_MJ
            ) {
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitJiazhu;
                });

                logger.debug("====================maizhuang==========0000000000000000000000000========");

                this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang });
                this.cancelTrustWholeAction();
                this.trustWhole();
            }
            else if (tData.roundNum == tData.roundAll && !tData.hasReadyBtn && !tData.freeBeginStart && (tData.gameType == GAME_TYPE.DOU_DI_ZHU_TY
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_HBTY
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_QC
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_JZ
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_LV_LIANG
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_LIN_FEN
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_XIN_ZHOU
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_DA_TONG
                //|| tData.gameType == GAME_TYPE.LV_LIANG_DA_QI
                || tData.gameType == GAME_TYPE.PAO_HU_ZI
                || tData.gameType == GAME_TYPE.PAO_HU_ZI_SR
                || tData.gameType == GAME_TYPE.LUO_DI_SAO
                || tData.gameType == GAME_TYPE.PAO_HU_ZI_King
                || tData.gameType == GAME_TYPE.PAO_HU_ZI_SR_King
                || tData.gameType == GAME_TYPE.ZP_LY_CHZ
                || tData.gameType == GAME_TYPE.HY_LIU_HU_QIANG
                || tData.gameType == GAME_TYPE.HY_SHI_HU_KA
                || tData.gameType == GAME_TYPE.LEI_YANG_GMJ
                || tData.gameType == GAME_TYPE.YONG_ZHOU_MJ
                || tData.gameType == GAME_TYPE.JIANG_HUA_MJ
                || tData.gameType == GAME_TYPE.JIANG_YONG_15Z
                || tData.gameType == GAME_TYPE.DAO_ZHOU_MJ
                || tData.gameType == GAME_TYPE.TY_ZHUANZHUAN
                || tData.gameType == GAME_TYPE.SHU_YANG
                || tData.gameType == GAME_TYPE.GUAN_NAN
                || tData.gameType == GAME_TYPE.GUAN_YUN
                || tData.gameType == GAME_TYPE.LIAN_YUN_GANG
                || tData.gameType == GAME_TYPE.GAN_YU
                || tData.gameType == GAME_TYPE.TUAN_TUAN_ZHUAN
                || tData.gameType == GAME_TYPE.SU_QIAN
                || tData.gameType == GAME_TYPE.SI_YANG_HH
                || tData.gameType == GAME_TYPE.SI_YANG
                || tData.gameType == GAME_TYPE.XIN_SI_YANG
                || tData.gameType == GAME_TYPE.XIN_PU_HZ
                || tData.gameType == GAME_TYPE.DONG_HAI
                || tData.gameType == GAME_TYPE.PAO_DE_KUAI
                || tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI
                || tData.gameType == GAME_TYPE.SHAO_YANG_ZI_PAI
                || tData.gameType == GAME_TYPE.WANG_DIAO_MA_JIANG
                || (tData.gameType == GAME_TYPE.XIANG_XIANG_GAO_HU_ZI && !tData.areaSelectMode.fullperson)
                || tData.gameType == GAME_TYPE.XUE_LIU
                || tData.gameType == GAME_TYPE.XUE_ZHAN
                || tData.gameType == GAME_TYPE.HAI_AN_MJ
                || tData.gameType == GAME_TYPE.HAI_AN_BAI_DA
                || tData.gameType == GAME_TYPE.DOU_DI_ZHU_HA
                || tData.gameType == GAME_TYPE.PAO_DE_KUAI_HAIAN
                || (tData.gameType == GAME_TYPE.LOU_DI_FANG_PAO_FA && !tData.areaSelectMode.fullperson)
                || (tData.gameType == GAME_TYPE.SHAO_YANG_FANG_PAO_FA && !tData.areaSelectMode.fullperson)
                || tData.gameType == GAME_TYPE.HENG_YANG_FANG_PAO_FA
                || tData.gameType == GAME_TYPE.HENG_YANG_SHIWUHUXI
                || tData.gameType == GAME_TYPE.XU_ZHOU_PEI_XIAN
                || (tData.gameType == GAME_TYPE.XIN_NING_MA_JIANG)
                || (tData.gameType == GAME_TYPE.SHAO_YANG_MA_JIANG)
                || (tData.gameType == GAME_TYPE.LENG_SHUI_JIANG_SHI_HU_DAO)
                || tData.gameType == GAME_TYPE.TY_HONGZHONG
                || tData.gameType == GAME_TYPE.XIANG_XIANG_HONG_ZHONG
                || (tData.gameType == GAME_TYPE.XIANG_XIANG_PAO_HU_ZI && !tData.areaSelectMode.fullperson)
                || tData.gameType == GAME_TYPE.PAO_HU_ZI_LR
                || tData.gameType == GAME_TYPE.PAO_HU_ZI_LR_King
                || tData.gameType == GAME_TYPE.YUE_YANG_FU_LU_SHOU
                || tData.gameType == GAME_TYPE.FU_LU_SHOU_ER_SHI_ZHANG
                || (tData.gameType == GAME_TYPE.XIANG_TAN_PAO_HU_ZI && !tData.areaSelectMode.fullperson)
                || tData.gameType == GAME_TYPE.HUAI_HUA_MA_JIANG
                || tData.gameType == GAME_TYPE.AN_HUA_MA_JIANG
                || tData.gameType == GAME_TYPE.AN_HUA_MA_JIANG_SW
                || tData.gameType == GAME_TYPE.NING_XIANG_KAI_WANG
                || tData.gameType == GAME_TYPE.YI_YANG_MA_JIANG
                || tData.gameType == GAME_TYPE.NAN_JING
                || tData.gameType == GAME_TYPE.GUI_ZHOU_PU_DING_MJ
                || tData.gameType == GAME_TYPE.GUI_ZHOU_XMY_GUI_YANG_ZHUO_JI
                || tData.gameType == GAME_TYPE.GUI_ZHOU_AN_SHUN_MJ
                || tData.gameType == GAME_TYPE.GUI_ZHOU_SAN_DING_LIANG_FANG
                || tData.gameType == GAME_TYPE.GUI_ZHOU_LIANG_DING_LIANG_FANG
                || tData.gameType == GAME_TYPE.GUI_ZHOU_SAN_DING_GUAI
                || tData.gameType == GAME_TYPE.GUI_ZHOU_ER_DING_GUAI
                || tData.gameType == GAME_TYPE.GUI_ZHOU_MEN_HU_XUE_LIU
                || tData.gameType == GAME_TYPE.GUI_ZHOU_LIANG_DING_YI_FANG
                || tData.gameType == GAME_TYPE.RU_GAO
                || tData.gameType == GAME_TYPE.RU_GAO_ER
                || tData.gameType == GAME_TYPE.BAI_PU_LIN_ZI
                || tData.gameType == GAME_TYPE.RU_GAO_SHUANG_JIANG
                || tData.gameType == GAME_TYPE.RU_DONG_SHUANG_JIANG
                || tData.gameType == GAME_TYPE.QU_TANG_23_ZHANG
                || (tData.gameType === GAME_TYPE.JIN_ZHONG_MJ && tData.maxPlayer === 2)
                // ||(tData.gameType == GAME_TYPE.JIN_ZHONG_KD &&  !this.canSelectDir())
                || (tData.gameType == GAME_TYPE.XU_PU_LAO_PAI && [1, 2].indexOf(tData.areaSelectMode.chongFenId) >= 0)
                || tData.gameType == GAME_TYPE.ER_REN_YI_FANG_MJ
                || tData.gameType == GAME_TYPE.YI_CHANG_XUE_LIU_MJ
                || tData.gameType == GAME_TYPE.HUAI_AN_ERZ
            )) {


                logger.debug("====================ready===========00000000000000000000000000000=======");
                tData.hasReadyBtn = true;
                tData.tState = TableState.waitReady;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitReady;
                });
                if (this.createParams.isNeedReady == 0 && !utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.LONG_HUI_BA_ZHA_DAN], this.createParams.gameType)) {
                    this.AllPlayerRun((pl) => {
                        if (this.pGameCode.mjPassCard) {
                            this.pGameCode.mjPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                        }
                        else if (this.pGameCode.pkPassCard) {
                            this.pGameCode.pkPassCard(pl, { eatFlag: 0 }, null, emptyFunction, this);
                        }
                    });
                    return;
                }
                this.NotifyAll("waitReady", {});

                logger.debug("waitReady =============RU_GAO=============   ", this.tData.roundNum);
            }
            else if (tData.gameType == GAME_TYPE.XU_PU_LAO_PAI && this.pGameCode.isNeedSelectChong(this)) {
                logger.debug("====================XU_PU_LAO_PAI==========0000000000000000000========");
                // 溆浦老牌冲选择流程
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitJiazhu;
                });
                var chongData = {};
                if (this.pGameCode.getSelectChongData) {
                    chongData = this.pGameCode.getSelectChongData(this);
                }
                this.NotifyAll("waitJiazhu", { chongData: chongData });
                logger.debug("====================XU_PU_LAO_PAI========== chongData = ", chongData);
            }
            else if (tData.gameType == GAME_TYPE.SHAO_YANG_MA_JIANG && tData.gameType == GAME_TYPE.SHAO_YANG_MA_JIANG && tData.areaSelectMode.isJiaChui && !tData.areaSelectMode.isLianChui || (tData.gameType == GAME_TYPE.SHAO_YANG_MA_JIANG && tData.areaSelectMode.isLianChui && tData.roundNum == tData.roundAll)) {
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    if (p.jiachuiNum < 0) {
                        p.mjState = TableState.waitJiazhu;
                    }
                });
                this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang });
            }
            else if ((tData.gameType == GAME_TYPE.SHAO_YANG_ZI_PAI || tData.gameType == GAME_TYPE.HY_SHI_HU_KA || tData.gameType == GAME_TYPE.LENG_SHUI_JIANG_SHI_HU_DAO) && tData.areaSelectMode.isJiaChui) { // 邵阳字牌 十胡卡 每小局发牌前选加锤
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.jiachuiNum = -1;
                });
                this.NotifyAll("waitJiazhu", {});
            }
            else if (tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI && tData.areaSelectMode.isJiaChui && tData.freeBeginStart && tData.roundNum == tData.roundAll) { //
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.jiachuiNum = -1;
                    p.mjState = TableState.waitJiazhu;
                });
                this.NotifyAll("waitJiazhu", {});
            }
            else if (tData.gameType == GAME_TYPE.GUI_ZHOU_GUI_YANG_ZHUO_JI && tData.areaSelectMode["gumai"]) {
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.gumaiValue = -1;
                });
                this.NotifyAll("waitJiazhu", {});
            }
            else if (tData.gameType == GAME_TYPE.GUI_ZHOU_AN_LONG_MJ && tData.areaSelectMode.gumaitype == 99) {//99是自由估卖
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.gumaiValue = -1;
                });
                this.NotifyAll("waitJiazhu", {});
            }
            else if (tData.gameType == GAME_TYPE.GUI_ZHOU_LI_PING_MJ && (tData.areaSelectMode.gumai == 1 && tData.roundNum == tData.roundAll || tData.areaSelectMode.gumai == 2)) {
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.gumaiValue = -1;
                });
                this.NotifyAll("waitJiazhu", {});
            }
            else if ((tData.gameType == GAME_TYPE.CHEN_ZHOU_ZI_PAI || tData.gameType == GAME_TYPE.GUI_YANG_ZI_PAI) && tData.areaSelectMode.piaoFen) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if (tData.gameType == GAME_TYPE.TONG_CHENG_GE_ZI_PAI) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.XU_PU_PAO_HU_ZI) && tData.areaSelectMode.isJiaChui) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.CHEN_ZHOU_MAO_HU_ZI) && tData.areaSelectMode.piaoFen) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.YUE_YANG_WAI_HU_ZI) && tData.areaSelectMode.piaoFen) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.XIANG_XI_2710) && tData.areaSelectMode.chongFen) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.POKER_96) && tData.areaSelectMode.piaoFen) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if ((tData.gameType == GAME_TYPE.YUE_YANG_FU_LU_SHOU) && (tData.areaSelectMode.piaoType < 3 || (tData.areaSelectMode.piaoType == 4 && tData.areaSelectMode.piaoFlag > 0))) {  // 1热飘 2冷飘 3不飘 4 固定飘分
                this.pGameCode.resetGameData(this);
            }
            else if ((tData.gameType == GAME_TYPE.FU_LU_SHOU_ER_SHI_ZHANG) && tData.areaSelectMode.piaoFlag > 1) {
                this.pGameCode.resetGameData(this);
            }
            else if ((tData.gameType == GAME_TYPE.XIANG_XIANG_GAO_HU_ZI || tData.gameType == GAME_TYPE.LOU_DI_FANG_PAO_FA) && tData.areaSelectMode.tuototuo > 0 && tData.roundAll == tData.roundNum) { // 湘乡告胡子、娄底放炮罚先准备，在打坨
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitJiazhu;
                });
                this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang });
            }
            else if ((tData.gameType == GAME_TYPE.XIN_NING_MA_JIANG) && tData.areaSelectMode.isJiaChui) {
                tData.tState = TableState.waitJiazhu;
                var t_this = this;
                var nextZhuang = t_this.pGameCode.getNextZhuang(this);
                var jiachuiNums = {};
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitJiazhu;
                    if (tData.areaSelectMode.isLianChui) {
                        if (nextZhuang == tData.lastZhuang && tData.uids.indexOf(p.uid) == tData.lastZhuang) {
                            p.mjState = TableState.isReady;
                        }
                    }
                    else if (tData.areaSelectMode.isZhuangShangChui && tData.uids.indexOf(p.uid) == nextZhuang) {
                        logger.debug("runStartGame  ???? ", p.uid);
                        // 庄家自动设置为加锤
                        p.jiachuiNum = 1; // 庄家默认为加锤
                        p.mjState = TableState.isReady;
                    }

                    jiachuiNums[p.uid] = p.jiachuiNum;
                });
                this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang, jiachuiNums: jiachuiNums });
            }
            else if (((tData.gameType == GAME_TYPE.TY_HONGZHONG || tData.gameType == GAME_TYPE.TY_ZHUANZHUAN) && tData.areaSelectMode["jiapiao"])
                || (tData.gameType == GAME_TYPE.YUE_YANG_YI_JIAO_LAI_YOU && tData.areaSelectMode["piaoFen"] == 6)
                || (tData.gameType == GAME_TYPE.HU_BEI_YI_JIAO_LAI_YOU && tData.areaSelectMode["piaoFen"] == 6)) {
                tData.tState = TableState.waitJiazhu;
                this.AllPlayerRun(function (p) {
                    p.mjState = TableState.waitJiazhu;
                });
                this.NotifyAll("waitJiazhu", { zhuang: tData.zhuang });
            }
            else if (tData.gameType == GAME_TYPE.HU_BEI_JING_SHAN_MJ) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if (tData.gameType == GAME_TYPE.XIAO_GAN_KA_WU_XING || this.tData.gameType == GAME_TYPE.SUI_ZHOU_KA_WU_XING) {
                //首局
                if (tData.roundNum == tData.roundAll) {
                    this.pGameCode.startChuZi(tData, this);
                } else {
                    this.pGameCode.startJiaZhu(tData, this);
                }
            }
            else if (tData.gameType === GAME_TYPE.TONG_CHENG_MJ) {
                this.pGameCode.startJiaZhu(tData, this);
            }
            else if (tData.gameType == GAME_TYPE.TY_ZHUANZHUAN && tData.areaSelectMode["piaoniao"] || tData.gameType == GAME_TYPE.ML_HONGZHONG && tData.areaSelectMode["piaoniao"] || tData.gameType == GAME_TYPE.ZHAO_TONG_MJ && tData.areaSelectMode["piaoniao"] || tData.gameType == GAME_TYPE.NING_XIANG_KAI_WANG && tData.areaSelectMode["piaoniao"]) {
                tData.tState = TableState.waitJiazhu;
                var chuoIdArr = this.pGameCode.getChuoNiaoPlayer(this);
                this.AllPlayerRun(function (p) {
                    if (chuoIdArr != 0 && chuoIdArr.indexOf(p.uid) >= 0 && p.jiazhuNum == 4) {
                        p.mjState = TableState.isReady;
                    }
                    else {
                        p.mjState = TableState.waitJiazhu;
                    }
                });
                this.NotifyAll("waitJiazhu", { chuoId: chuoIdArr });
            }
            else if ((tData.gameType == GAME_TYPE.HY_LIU_HU_QIANG ||
                tData.gameType == GAME_TYPE.HY_SHI_HU_KA ||
                tData.gameType == GAME_TYPE.HENG_YANG_SHIWUHUXI ||
                tData.gameType == GAME_TYPE.SHAO_YANG_ZI_PAI ||
                tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI ||
                tData.gameType == GAME_TYPE.SHAO_YANG_FANG_PAO_FA ||
                tData.gameType == GAME_TYPE.HENG_YANG_FANG_PAO_FA ||
                tData.gameType == GAME_TYPE.HY_ER_PAO_HU_ZI ||
                tData.gameType == GAME_TYPE.ZP_LY_CHZ ||
                tData.gameType == GAME_TYPE.LENG_SHUI_JIANG_SHI_HU_DAO ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI_King ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI_LR_King ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI_LR ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI_SR_King ||
                tData.gameType == GAME_TYPE.PAO_HU_ZI_SR ||
                tData.gameType == GAME_TYPE.LUO_DI_SAO ||
                tData.gameType == GAME_TYPE.JIANG_YONG_15Z ||
                tData.gameType == GAME_TYPE.ML_HONG_ZI ||
                tData.gameType == GAME_TYPE.XIANG_YIN_ZHUO_HONG_ZI ||
                tData.gameType == GAME_TYPE.YI_YANG_WAI_HU_ZI ||
                tData.gameType == GAME_TYPE.NAN_XIAN_GUI_HU_ZI ||
                tData.gameType == GAME_TYPE.YUAN_JIANG_GUI_HU_ZI ||
                tData.gameType == GAME_TYPE.YUE_YANG_WAI_HU_ZI ||
                tData.gameType == GAME_TYPE.YY_AN_HUA_PAO_HU_ZI ||
                tData.gameType == GAME_TYPE.CHEN_ZHOU_ZI_PAI ||
                tData.gameType == GAME_TYPE.GUI_YANG_ZI_PAI ||
                tData.gameType == GAME_TYPE.CHEN_ZHOU_MAO_HU_ZI ||
                tData.gameType == GAME_TYPE.NING_XIANG_PAO_HU_ZI ||
                tData.gameType == GAME_TYPE.XIANG_XI_2710 ||
                tData.gameType == GAME_TYPE.YUE_YANG_PENG_HU ||
                tData.gameType == GAME_TYPE.HAN_SHOU_PAO_HU_ZI ||
                tData.gameType == GAME_TYPE.DA_YE_ZI_PAI ||
                tData.gameType == GAME_TYPE.HUAI_HUA_HONG_GUAI_WAN)  // 带切牌功能的字牌
                && tData.areaSelectMode.isManualCutCard) {
                // 衡阳字牌 切牌
                this.pGameCode.startShuffleCards(this);
                this.cancelTrustWholeAction();
                this.trustWhole();
            }
            else if (tData.roundNum == tData.roundAll && !tData.hasReadyBtn && (tData.maxPlayer == 4 || tData.maxPlayer == 3) && this.canSelectDir()) {
                tData.hasReadyBtn = true;
            }
            else if (this.pGameCode.isDoPiaofen && this.pGameCode.doPiaofen && this.pGameCode.isDoPiaofen(this)) {
                this.pGameCode.doPiaofen(this);
            }
            else if (this.pGameCode.isDoAfterReady && this.pGameCode.doAfterReady && this.pGameCode.isDoAfterReady(this)) {
                this.pGameCode.doAfterReady(this);
            }
            else if (tData.gameType == GAME_TYPE.YONG_ZHOU_BAO_PAI && tData.areaSelectMode.jiaChui) {
                this.pGameCode.doJiaChui(this);
            }
            else {
                this.runStartGame();
            }
        }
    };
    
    //真正开始牛牛游戏，开始发牌了
    Table.prototype.runStartGameNN = function() {
        if (this.tData.roundNum <= -2)
        {
            return;
        }
       
        if (this.tData.roundNum == this.tData.roundAll)
        {
            this.startTime = Date.now();
            this.tData.startStatus = 1;
        }
        if (this.createParams.clubId)
        {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'round', {ruleId: this.createParams.ruleId, roomNum: this.tableid, roundNum: this.tData.roundAll - this.tData.roundNum + 1 + (this.tData.extraNum || 0)}, emptyFunction);
        }

        if (this.pGameCode.doBeforeHands) {
            this.pGameCode.doBeforeHands(this);
        }
       
        var tData = this.tData;
        tData.cardNext = 0;//重置当前卡牌index
        delete tData.roundKey;

        var usercards = [];
        this.cards = [];
        if(this.createParams.zhuangType != 3 && this.createParams.zhuangType != 5)
        {
            this.cards = this.pGameCode.majiang.randomCards(this);
        }
        tData.shuffleCardsNum = this.cards.length;
        tData.tState = TableState.waitCard;

        var cards = this.cards;
        var jiazhuNums = {};
        var winalls = {};
      
        var maxPlayer = tData.rungingUids.length;
        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = this.players[tData.rungingUids[i]];
            pl.init();
            if (this.pGameCode.initPlayer)
            {
                this.pGameCode.initPlayer(pl);
            }
            
            pl.mjState = TableState.waitCard;
            winalls[pl.uid] = pl.winall;

            var handCount = this.pGameCode.majiang.handCount;
            if(usercards[pl.uid] && usercards[pl.uid].length > 0)
            {
                if(this.createParams.zhuangType != 3 && this.createParams.zhuangType != 5)
                {
                    pl.mjhand = utils.clone(usercards[pl.uid]);    
                }
            }
            else
            {
                for (var j = 0; j < handCount; j++)
                {
                    //明牌抢庄一开始每个玩家的手牌都处理好了
                    if(this.createParams.zhuangType != 3 && this.createParams.zhuangType != 5)
                    {
                        var newCard = cards[tData.cardNext++];
                        pl.mjhand.push(newCard);
                    }
                    else
                    {
                        var newCard = pl.mjTmpHand[j];
                        pl.mjhand.push(newCard);
                    }
                }
            }

            jiazhuNums[pl.uid] = pl.jiazhuNum;
        }

        //针对牛牛处理哪些人是动态加入的（绘制手牌用）
        var centerJoin = {};
        for (var i = 0; i < tData.maxPlayer; i++)
        {
            var p = this.players[tData.uids[i]];
            if (p) {
                centerJoin[p.uid] = p.centerJoinNN;
            }
        }

        var mjhandDatas = {};
        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = this.players[tData.rungingUids[i]];
            var mjhandMsg = {
                mjhand : JSON.parse(JSON.stringify(pl.mjhand)),
                tData: tData,
                jiazhuNums: jiazhuNums,
                winalls: winalls,
                centerJoinNN:JSON.parse(JSON.stringify(centerJoin)),              //牛牛添加玩家是否是动态加入
            }; 
            pl.notify('mjhand', mjhandMsg);

            mjhandDatas[pl.uid] = {
                mjhand: pl.mjhand
            };
        }

        cloneDataAndPush(this.mjlog, "mjhand", {cards: this.cards, tData: tData, jiazhuNums: jiazhuNums, winalls: winalls}, {mjhandDatas: mjhandDatas});
    };

    //炸金花开始发牌
    Table.prototype.runStartGameZJH = function()
    {
        if (this.tData.roundNum <= -2)
        {
            return;
        }

        //设置游戏开始状态
        this.tData.gameRuning = true;

        if (this.tData.roundNum == this.tData.roundAll)
        {
            this.startTime = Date.now();
            this.tData.startStatus = 1;
        }
        if (this.createParams.clubId)
        {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'round', {ruleId: this.createParams.ruleId, roomNum: this.tableid, roundNum: this.tData.roundAll - this.tData.roundNum + 1 + (this.tData.extraNum || 0)}, emptyFunction);
        }

        if (this.mjlog.length == 0) {
            cloneDataAndPush(this.mjlog, "initSceneData", this.initSceneData());
            if(this.trustWholeLog && this.trustWholeLog.length > 0){
                var cmdLog = this.trustWholeLog.pop();
                cloneDataAndPush(this.mjlog, cmdLog.cmd, cmdLog.data);
            }
        }

        if (this.pGameCode.doBeforeHands) {
            this.pGameCode.doBeforeHands(this);
        }
       
        var tData = this.tData;
        tData.cardNext = 0;//重置当前卡牌index
        delete tData.roundKey;

        this.cards = this.pGameCode.majiang.randomCards(this);
        tData.shuffleCardsNum = this.cards.length;
        tData.tState = TableState.waitCard;

        var cards = this.cards;
        var winalls = {};
        var jiazhuNums = {};
        var handCount = this.pGameCode.majiang.handCount;
        var maxPlayer = tData.rungingUids.length;
        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = this.players[tData.rungingUids[i]];
            pl.init();
            if (this.pGameCode.initPlayer)
            {
                this.pGameCode.initPlayer(pl);
            }
            
            pl.mjState = TableState.waitCard;

            winalls[pl.uid] = pl.winall;

            for (var j = 0; j < handCount; j++)
            {
                var newCard = cards[tData.cardNext++];
                pl.mjhand.push(newCard);
            }

            jiazhuNums[pl.uid] = pl.jiazhuNum;
        }

        //处理哪些人是动态加入的（绘制手牌用）
        var centerJoin = {};
        for (var i = 0; i < tData.maxPlayer; i++)
        {
            var p = this.players[tData.uids[i]];
            if (p) {
                centerJoin[p.uid] = p.centerJoinNN;
            }
        }

        var mjhandDatas = {};
        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = this.players[tData.rungingUids[i]];
            var mjhandMsg = {
                mjhand : JSON.parse(JSON.stringify([0,0,0])),
                tData: tData,
                jiazhuNums: jiazhuNums,
                winalls: winalls,
                centerJoinNN:JSON.parse(JSON.stringify(centerJoin)),              //玩家是否是动态加入
            };
            pl.notify('mjhand', mjhandMsg);

            mjhandDatas[pl.uid] = {
                mjhand: pl.mjhand
            };
        }

        cloneDataAndPush(this.mjlog, "mjhand", {cards: this.cards, tData: tData, jiazhuNums: jiazhuNums, winalls: winalls}, {mjhandDatas: mjhandDatas});

        if (this.pGameCode.doAfterHands) {
            this.pGameCode.doAfterHands(this);
        }
    };

    //捞腌菜开始发牌
    Table.prototype.runStartGameLYC = function()
    {
        if (this.tData.roundNum <= -2)
        {
            return;
        }

        if (this.tData.roundNum == this.tData.roundAll)
        {
            this.startTime = Date.now();
            this.tData.startStatus = 1;
        }
        if (this.createParams.clubId)
        {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'round', {ruleId: this.createParams.ruleId, roomNum: this.tableid, roundNum: this.tData.roundAll - this.tData.roundNum + 1 + (this.tData.extraNum || 0)}, emptyFunction);
        }

        if (this.pGameCode.doBeforeHands) {
            this.pGameCode.doBeforeHands(this);
        }
       
        var tData = this.tData;
        tData.cardNext = 0;//重置当前卡牌index
        delete tData.roundKey;

        this.cards = this.pGameCode.majiang.randomCards(this);
        tData.shuffleCardsNum = this.cards.length;
        tData.tState = TableState.waitCard;

        var cards = this.cards;
        var winalls = {};
        var jiazhuNums = {};
        var handCount = this.pGameCode.majiang.handCount;
        var maxPlayer = tData.rungingUids.length;

        for (var i = 0; i < maxPlayer; i++)
        {
            var pl = this.players[tData.rungingUids[i]];
            pl.init();
            if (this.pGameCode.initPlayer)
            {
                this.pGameCode.initPlayer(pl);
            }
            
            pl.mjState = TableState.waitCard;

            winalls[pl.uid] = pl.winall;

            for (var j = 0; j < handCount; j++)
            {
                var newCard = cards[tData.cardNext++];
                pl.mjhand.push(newCard);
            }

            jiazhuNums[pl.uid] = pl.jiazhuNum;
        }

        //处理哪些人是动态加入的（绘制手牌用）
        var centerJoin = {};
        for (var i = 0; i < tData.maxPlayer; i++)
        {
            var p = this.players[tData.uids[i]];
            if (p) {
                centerJoin[p.uid] = p.centerJoinNN;
            }
        }

        var mjhandDatas = {};
        for (let i = 0; i < maxPlayer; i++)
        {
            let pl = this.players[tData.rungingUids[i]];
            let mjhandMsg = {
                uid: pl.uid,
                mjhand : JSON.parse(JSON.stringify(pl.mjhand)),
                tData: tData,
                jiazhuNums: jiazhuNums,
                winalls: winalls,
                centerJoinNN:JSON.parse(JSON.stringify(centerJoin)),
            }; 
            pl.notify('mjhand', mjhandMsg);

            mjhandDatas[pl.uid] = {
                mjhand: pl.mjhand
            };
        }

        cloneDataAndPush(this.mjlog, "mjhand", {cards: this.cards, tData: tData, jiazhuNums: jiazhuNums, winalls: winalls}, {mjhandDatas: mjhandDatas});

        //发空牌
        var mjhandMsg = {};
        for (let i = 0; i < maxPlayer; i++)
        {
            let pl = this.players[tData.rungingUids[i]];
            let _mjhand = pl.mjhand.concat();
            mjhandMsg[pl.uid] = _mjhand;
        }
        mjhandMsg.CardCount = handCount;
        cloneDataAndPush(this.mjlog, "NNSendFourCard", mjhandMsg);
        
        var emptyCards = {};
        emptyCards.CardCount = handCount;
        emptyCards.zhuang = tData.zhuang;
        for (let i = 0; i < maxPlayer; i++)
        {
            let pl = this.players[tData.rungingUids[i]];
            emptyCards[pl.uid] = [0,0];
        }
        this.NotifyAll("NNSendFourCard", emptyCards);
    };

    //真正开始游戏，开始发牌了
    Table.prototype.runStartGame = function () {
        if (this.tData.roundNum <= -2) {
            return;
        }

        if (this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA && this.tData.gameRuning) {
            return;
        }
        if (this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA)
            this.tData.gameRuning = true;

        if (this.tData.roundNum == this.tData.roundAll) {
            this.startTime = Date.now();
        }
        if (this.createParams.clubId) {
            if (utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.SHAO_YANG_BO_PI, GAME_TYPE.HENG_YANG_FANG_PAO_FA,
            GAME_TYPE.SHAO_YANG_FANG_PAO_FA, GAME_TYPE.LONG_HUI_BA_ZHA_DAN, GAME_TYPE.XIANG_XIANG_GAO_HU_ZI,
            GAME_TYPE.LOU_DI_FANG_PAO_FA, GAME_TYPE.DIAN_TUO], this.createParams.gameType)) {
                var maxScore = 0;
                if (this.teams && !utils.isEmptyObject(this.teams)) {
                    for (var key in this.teams) {
                        if (this.teams[key].winall > maxScore) maxScore = this.teams[key].winall;
                    }
                } else {
                    this.AllPlayerRun(function (p) {
                        if (p.winall > maxScore) maxScore = p.winall;
                    });
                }
                app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'score', { ruleId: this.createParams.ruleId, roomNum: this.tableid, roundNum: this.tData.roundAll - this.tData.roundNum + 1 + (this.tData.extraNum || 0), maxScore: maxScore }, emptyFunction);
            } else {
                app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'round', { ruleId: this.createParams.ruleId, roomNum: this.tableid, roundNum: this.tData.roundAll - this.tData.roundNum + 1 + (this.tData.extraNum || 0) }, emptyFunction);
            }
        }

        var tData = this.tData;
        tData.cardNext = 0;//重置当前卡牌index
        tData.hunCard = -1;// 重置混牌
        tData.currCard = -1;
        tData.xingPlayer = -1;//重置跑胡子4人玩法中的醒家
        tData.huangNum = 14;
        delete tData.roundKey;
        if (tData.gameType == GAME_TYPE.XU_ZHOU) {
            tData.huangNum = 12;
        }
        else if (tData.gameType == GAME_TYPE.TONG_HUA || tData.gameType == GAME_TYPE.TAO_JIANG_MA_JIANG) {
            tData.huangNum = 8;
        }
        tData.genpaiCount = 0;
        tData.drawType = 0; // 0正常摸牌 1杠摸牌 (耒阳麻将 添加)

        if (this.pGameCode.generateCards)
            this.cards = this.pGameCode.generateCards(this);
        else
            this.cards = this.pGameCode.majiang.randomCards(tData.areaSelectMode, tData, this);

        tData.cardNextTemp = tData.cardNext;
        if (this.pGameCode.randomHunCard) {
            //如果有混玩法，才走随机混子的逻辑
            tData.hunCard = this.pGameCode.randomHunCard(this.cards, tData);
        }
        //--------------BEGIN---------------
        //--------降低免扣比例调牌功能模块-------


        tData.shuffleCardsNum = this.cards.length;
        tData.lastPutPlayer = tData.curPlayer;
        tData.tState = TableState.waitCard;

        // 在重置winner之前先计算庄家，确保上一局的获胜者能正确成为下一局的庄家
        if (this.pGameCode.countZhuang) {
            this.pGameCode.countZhuang(tData, this);
        }

        tData.winner = -1;//计算庄完毕后,重置winner值
        if (this.mjlog.length == 0) {
            cloneDataAndPush(this.mjlog, "initSceneData", this.initSceneData());
            if (this.trustWholeLog && this.trustWholeLog.length > 0) {
                var cmdLog = this.trustWholeLog.pop();
                cloneDataAndPush(this.mjlog, cmdLog.cmd, cmdLog.data);
            }
        }
        if (this.pGameCode.doBeforeHands) {
            this.pGameCode.doBeforeHands(this);
        }
        var cards = this.cards;
        var flowers = {};
        var jiazhuNums = {};
        var jiachuiNums = {};
        var guMaiNums = {};
        var mjhandDatas = {};
        var winalls = {};
        var piaoFen = {};
        var mjhandFours = {};
        var shuffleUids = [];
        for (var i = 0; i < tData.maxPlayer; i++) {
            var currUid = tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer];
            if ((this.tableid + tData.roundNum) % 2) { //交换牌
                currUid = tData.uids[(i + tData.zhuang + 1 + tData.maxPlayer) % tData.maxPlayer];
            }

            //牛牛处理操作
            if(tData.gameType == GAME_TYPE.NIU_NIU || tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) {
                const maxPlayer = tData.rungingUids.length;
                currUid = tData.rungingUids[(i + tData.zhuang + tData.maxPlayer) % maxPlayer];
            } 

            var pl = this.players[currUid];
            if (this.createParams.clubId && pl.info.happybean) {
                if (typeof (pl.shuffled) != "undefined" && utils.isStrNotEmpty(pl.shuffled) && pl.shuffled > 0 && utils.isStrNotEmpty(this.createParams.payWay)) { // 洗牌功能，开始游戏进行扣费
                    shuffleUids.push(pl.uid);
                    pl.info.happybean = pl.info.happybean - this.createParams.xiPaiLimitScore;
                    pl.shuffleNum++;
                }
            }

            pl.init();
            if (this.pGameCode.initPlayer) {
                this.pGameCode.initPlayer(pl);
            }
            pl.mjState = TableState.waitCard;
            winalls[pl.uid] = pl.winall;
            if (tData.gameType == GAME_TYPE.PAO_HU_ZI_SR || tData.gameType == GAME_TYPE.PAO_HU_ZI_SR_King || (tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI && tData.areaSelectMode.zuoXing) || (tData.gameType == GAME_TYPE.SHAO_YANG_FANG_PAO_FA && tData.areaSelectMode.zuoXing) || (tData.gameType == GAME_TYPE.HENG_YANG_FANG_PAO_FA && tData.areaSelectMode.zuoXing)) {
                var index = tData.uids.indexOf(pl.uid);
                if (index == tData.xingPlayer) {
                    continue;
                }
            }
            var handCount = this.pGameCode.majiang.handCount;
            if (tData.gameType == GAME_TYPE.HY_LIU_HU_QIANG) {
                handCount = tData.areaSelectMode.is21Zhang ? 20 : 14;
            }
            else if (tData.gameType == GAME_TYPE.HY_SHI_HU_KA) {
                handCount = 20;
            }
            else if (tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI && tData.maxPlayer == 4 && !tData.areaSelectMode.zuoXing) {
                handCount = 14;
            }
            else if (tData.gameType == GAME_TYPE.DA_TONG_ZI_SHAO_YANG || tData.gameType == GAME_TYPE.BAN_BIAN_TIAN_ZHA || tData.gameType == GAME_TYPE.DIAN_TUO) {
                handCount = tData.handCount;
            }
            else if (tData.gameType == GAME_TYPE.CHEN_ZHOU_ZI_PAI || tData.gameType == GAME_TYPE.GUI_YANG_ZI_PAI) {
                if (tData.maxPlayer == 4 || tData.areaSelectMode.isShiWuZhang) {
                    handCount = 14;
                }
                else {
                    handCount = 20;
                }
            }
            else if (tData.gameType == GAME_TYPE.LONG_HUI_BA_ZHA_DAN) {
                handCount = tData.handCount;
                if (tData.maxPlayer == 4 && tData.deckNum == 1 && tData.areaSelectMode.hasJoker && i <= 1) {
                    handCount++;
                } else if (tData.maxPlayer == 4 && tData.deckNum == 3 && !tData.areaSelectMode.addJoker && !tData.areaSelectMode.noJoker && i <= 1) {
                    handCount++;
                }
            }
            else if (tData.gameType == GAME_TYPE.YUE_YANG_FU_LU_SHOU && tData.maxPlayer == 4 && i == 2) {
                handCount = 2; // 四人拉牛位置只发两张牌
            }
            else if (tData.gameType == GAME_TYPE.YONG_ZHOU_LAO_CHUO) {
                if (tData.maxPlayer == 4) {
                    handCount = 20;
                } else {
                    handCount = 26;
                }
            }
            if (this.pGameCode.getHandCount) {
                handCount = this.pGameCode.getHandCount(this);
            }

            for (var j = 0; j < handCount; j++) {
                var newCard = cards[tData.cardNext++];
                while (this.pGameCode.isHardFlower && this.pGameCode.isHardFlower(newCard, tData)) {
                    pl.mjflower.push(newCard);
                    pl.putType = 5;
                    tData.putType = 5;
                    newCard = cards[tData.cardNext++];
                }
                pl.mjhand.push(newCard);
            }

            if (this.pGameCode.tingList) {
                pl.tingListAfterPut = this.pGameCode.tingList(pl, this);
            }
            // 初始化杠分
            pl.gangScore = 0;
            flowers[pl.uid] = pl.mjflower;
            jiazhuNums[pl.uid] = pl.jiazhuNum;
            jiachuiNums[pl.uid] = pl.jiachuiNum;
            guMaiNums[pl.uid] = pl.gumaiValue;
            piaoFen[pl.uid] = pl.piaoFen;
        }

        if (tData.gameType == GAME_TYPE.FEN_XI_YING_KOU) {
            var mCard = this.cards.pop();
            tData.hunCard = mCard;
            cards.push(tData.hunCard);
        }
        if (this.pGameCode.doBeforeSendHands) {
            this.pGameCode.doBeforeSendHands(this);
        }

        if (tData.gameType == GAME_TYPE.PAO_DE_KUAI_TY) {
            this.pdkOtherCards = tData.otherCards;
            tData.otherCards = [];
        }

        for (var i = 0; i < tData.maxPlayer; i++) {
            var pl = this.players[tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]];
            var mjhandMsg = {
                mjhand: JSON.parse(JSON.stringify(pl.mjhand)),
                linkZhuang: pl.linkZhuang,
                mjflower: pl.mjflower,
                tData: tData,
                flowers: flowers,
                jiazhuNums: jiazhuNums,
                jiachuiNums: jiachuiNums,
                piaoFen: piaoFen,
                winalls: winalls,
                firstFlower8: pl.firstFlower8,
                eatFlag: pl.eatFlag,
                tingListAfterPut: pl.tingListAfterPut,
                isInitHandAllBlack: pl.isInitHandAllBlack, // 汨罗红字添加
            };
            if (tData.gameType == GAME_TYPE.XIANG_SHUI_MJ) {
                mjhandMsg.dirNumber = pl.dirNumber;
            }
            if (tData.gameType == GAME_TYPE.GUAN_YUN && i > 0) { // 灌云起手听
                pl.isTianting = mjhandMsg.isTianting = this.pGameCode.majiang.canTing(pl.mjhand);
            }
            if (tData.gameType == GAME_TYPE.JIN_ZHONG_LI_SI) {
                mjhandMsg.mjhandFour = pl.mjhandFour;
                mjhandFours[pl.uid] = pl.mjhandFour;
            }
            if (tData.gameType == GAME_TYPE.EN_SHI_SHAO_HU) {
                pl.canNotPutCard = this.pGameCode.majiang.getCanNotPutCard(pl.mjhand);
                mjhandMsg.canNotPutCard = pl.canNotPutCard;
            }

            if (tData.gameType == GAME_TYPE.YONG_ZHOU_LAO_CHUO) {
                mjhandMsg.chuoCards = this.pGameCode.majiang.getInitChuoCards(pl);
                mjhandMsg.mjhand = JSON.parse(JSON.stringify(pl.mjhand));
            }

            if(tData.gameType == GAME_TYPE.ZHA_JIN_HUA){//扎金花不发手牌
                mjhandMsg.mjhand = JSON.parse(JSON.stringify([0,0,0]));
                mjhandMsg.uid = pl.uid;
                logger.debug("++++++++++++++++++++++++ 金花发牌 mjhandMsg.uid = " + mjhandMsg.uid);
            }

            pl.notify('mjhand', mjhandMsg);
            mjhandDatas[pl.uid] = {
                mjhand: pl.mjhand,
                linkZhuang: pl.linkZhuang,
                tingListAfterPut: pl.tingListAfterPut,
                mjflower: pl.mjflower,
                dirNumber: pl.dirNumber
            };
        }
        tData.putType = 0;
        //这里的逻辑是先把curPlayer修改成最后一个人，然后发牌的话会+1发牌，就变成庄发牌了
        tData.curPlayer = (tData.curPlayer + (tData.maxPlayer - 1)) % tData.maxPlayer;
        cloneDataAndPush(this.mjlog, "mjhand", { cards: this.cards, tData: tData, flowers: flowers, jiazhuNums: jiazhuNums, piaoFen: piaoFen, jiachuiNums: jiachuiNums, guMaiNums: guMaiNums, winalls: winalls, mjhandFours: mjhandFours }, { mjhandDatas: mjhandDatas });
        if (this.pGameCode.drawLastCard) { // 耒阳字牌庄家摸最后一张手牌(亮张)
            this.pGameCode.drawLastCard(this);
        }
        if (this.pGameCode.doAfterHands) {
            this.pGameCode.doAfterHands(this);
        }

        //!this.pGameCode.drawLastCard用于字牌判断，执行drawLastCard方法之后，不在执行sendNewCard
        if (this.pGameCode.sendNewCard && !this.pGameCode.drawLastCard) {
            this.pGameCode.sendNewCard(this, true);//true 表示是摸的第一张牌
        }
        if (this.pGameCode.doAfterFirstCard) {
            this.pGameCode.doAfterFirstCard(this);
        }
        if (tData.gameType == GAME_TYPE.NAN_JING) { // 南京起手花杠
            var endGame = false;
            for (var i = 0; i < tData.maxPlayer; i++) {
                var pl = this.players[tData.uids[(i + tData.zhuang) % tData.maxPlayer]];
                pl.mjflower.sort(function (a, b) { return a - b; });
                logger.debug("花牌", pl.mjflower);
                for (var j = 0; j < pl.mjflower.length; j++) {
                    if (!endGame && this.pGameCode.majiang.isHuagang(pl.mjflower, pl.mjflower[j])) {
                        logger.debug("j==>", j, pl.mjflower[j])
                        endGame = this.pGameCode.payCash(pl, this, "花杠", 10);
                        tData.nextBixiahu = true;
                        j += 3;
                    }
                }
            }
        }
    };

    //连赢连输平衡功能(目前只有安化跑胡子)
    Table.prototype.gameBalanceFunction = function () {
        //记录本局换牌
        var tData = this.tData;
        this.gameBalancePlayer = tData.BalanceId;
        var handCount = this.pGameCode.majiang.handCount;
        var maxChangeCard = [];
        var recordArry = [];
        var score = 0;
        var changeCardsNum = tData.changeCardsNum;
        while (changeCardsNum--) {
            this.cards = this.pGameCode.majiang.randomCards(tData.areaSelectMode, tData, this);
            tData.cardNext = tData.cardNextTemp;
            if (this.pGameCode.randomHunCard) {
                tData.hunCard = this.pGameCode.randomHunCard(this.cards, tData);
            }
            var tCards = this.cards.slice();
            var cardNext = tData.cardNext;
            var playerHandScore = [];
            for (var i = 0; i < tData.maxPlayer; i++) {
                var playerHands = [];
                for (var j = 0; j < handCount; j++) {
                    var newCard = tCards[cardNext++];
                    while (this.pGameCode.isHardFlower && this.pGameCode.isHardFlower(newCard, tData)) {
                        newCard = tCards[cardNext++];
                    }
                    playerHands.push(newCard);
                }
                playerHands.sort(function (a, b) { return a - b; });
                playerHandScore[i] = this.pGameCode.versionCalculateScore(playerHands, this);
            }
            var maxScoreCards = this.cards.slice();
            var Pscore = this.pGameCode.calScoreFormula(playerHandScore, this);
            if (score == 0 || score < Pscore) {
                recordArry[0] = tData.otherCards;
                recordArry[1] = tData.showCardIndex;
                recordArry[2] = tData.showCard;
                recordArry[3] = tData.hunCard;
                maxChangeCard = playerHandScore;
                maxChangeCard.push(Pscore);
                maxChangeCard.push(maxScoreCards);
            }
        }
        this.cards = maxChangeCard[maxChangeCard.length - 1];
        tData.otherCards = recordArry[0];
        tData.showCardIndex = recordArry[1];
        tData.showCard = recordArry[2];
        tData.hunCard = recordArry[3];
        //打印显示代码
        for (var j = 0; j < tData.maxPlayer; j++) {
            logger.debug(j + "位子手牌分值" + maxChangeCard[j]);
        }
        var ScoreSeat = 0;
        for (var j = 1; j < tData.maxPlayer; j++) {
            if (tData.blanceChoiceCards == 1 && maxChangeCard[ScoreSeat] < maxChangeCard[j]) {
                ScoreSeat = j;
            }
            else if (tData.blanceChoiceCards == -1 && maxChangeCard[ScoreSeat] > maxChangeCard[j]) {
                ScoreSeat = j;
            }
        }
        var playerSeat = 0;
        for (var i = 0; i < tData.maxPlayer; i++) {
            if (tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer] == tData.BalanceId) {
                playerSeat = i;
            }
        }
        logger.debug("需要换牌人目前的位子", playerSeat);
        logger.debug("需要换哪个位子的牌", ScoreSeat);
        logger.debug("换牌结束,选择分值最大一次手牌: " + maxChangeCard[maxChangeCard.length - 2]);
        logger.debug("牌型: " + JSON.stringify(this.cards));
        //手牌最大分值(最小分值)位子和需要换牌玩家位子不符合则需要换this.cards的顺序
        if (ScoreSeat != playerSeat) {
            for (var i = 0; i < handCount; i++) {
                if (ScoreSeat == 0) {
                    var temp = this.cards[handCount * playerSeat];
                    this.cards[handCount * playerSeat] = this.cards[handCount];
                    this.cards[handCount] = temp;
                }
                else if (playerSeat == 0) {
                    var temp = this.cards[handCount * ScoreSeat];
                    this.cards[handCount * ScoreSeat] = this.cards[handCount];
                    this.cards[handCount] = temp;
                }
                else {
                    var temp = this.cards[handCount * ScoreSeat];
                    this.cards[handCount * ScoreSeat] = this.cards[handCount * playerSeat];
                    this.cards[handCount * playerSeat] = temp;
                }
            }
        }
        logger.debug("手牌最大分值(最小分值)位子和需要换牌玩家位子不符,换牌后全部牌型: " + JSON.stringify(this.cards));
    }

    //换牌
    Table.prototype.changePlayersCards = function () {
        var tData = this.tData;
        var maxWinone = this.players[tData.uids[0]].winone;
        var minWinone = this.players[tData.uids[0]].winone;
        var putCardsPattern = 0;
        var lastPattern = 0;
        lastPattern = tData.lastPattern;
        tData.lastPattern = 0;
        var luckPlayer = 0;
        var lastLuckyPlayer = tData.lastLuckyPlayer;
        tData.lastLuckyPlayer = 0;
        var maxUids = [];
        var minUids = [];
        var playersAllScore = {};
        var minWinScore = null;
        var maxWinScore = null;
        for (var i = 0; i < tData.maxPlayer; i++) {
            var pl = this.players[tData.uids[i]];
            if (pl) {
                maxWinone = Math.max(maxWinone, pl.winone);
                minWinone = Math.min(minWinone, pl.winone)
            }
        }
        //联盟个人排名平衡(两个调整互斥，不能同时进行)
        if (this.leagueRankBalance && this.gameTypeScoreRank && this.gameTypeScoreRank.list.length > 5 && (tData.roundAll < 2 * tData.roundNum || !this.createParams.fangkaFree)) {
            logger.debug("进入----联盟个人平衡调牌", JSON.stringify(this.gameTypeScoreRank));
            logger.info("联盟个人平衡调牌", tData.gameType);
            for (var i = 0; i < this.gameTypeScoreRank.list.length; i++) {
                if (tData.uids.indexOf(this.gameTypeScoreRank.list[i].userId) >= 0) {
                    if (maxUids.length == 0 || maxUids[0] < this.gameTypeScoreRank.list[i].score) {
                        maxUids[0] = this.gameTypeScoreRank.list[i].score;
                        var maxWinUid = this.gameTypeScoreRank.list[i].userId;
                    }
                    if (minUids.length == 0 || minUids[0] > this.gameTypeScoreRank.list[i].score) {
                        minUids[0] = this.gameTypeScoreRank.list[i].score;
                        var minWinUid = this.gameTypeScoreRank.list[i].userId;
                    }
                }
            }
            if (minWinUid && minUids[0] <= this.gameTypeScoreRank.list[Math.ceil(0.8 * (this.gameTypeScoreRank.list.length - 1))].score) {
                luckPlayer = minWinUid;
                putCardsPattern = 1;
            }
            else if (maxWinUid && maxUids[0] >= this.gameTypeScoreRank.list[Math.floor(0.2 * (this.gameTypeScoreRank.list.length - 1))].score) {
                luckPlayer = maxWinUid;
                putCardsPattern = -1;
            }
            logger.info("桌子ID", tData.tableid, "玩法", tData.gameType, "局数", tData.roundNum, "选择调牌玩家", luckPlayer, putCardsPattern, minWinUid, maxWinUid);
            if (luckPlayer && (!lastLuckyPlayer || luckPlayer != lastLuckyPlayer ||
                //玩家需要赢分调牌没赢继续调牌
                (this.players[luckPlayer].winone < maxWinone && lastPattern > 0) ||
                //玩家需要掉分调牌没输继续调牌
                (this.players[luckPlayer].winone > minWinone && lastPattern < 0))
            ) {
                var randomValue = tData.freeFangKaConfig["调牌概率"] ? tData.freeFangKaConfig["调牌概率"] : 0.7;
                if (Math.random() < randomValue) {
                    this.restartPutCards(luckPlayer, putCardsPattern);
                    tData.lastLuckyPlayer = luckPlayer;
                    tData.lastPattern = putCardsPattern;
                }
            }
        }
        //房内免扣调整
        if (this.createParams.fangkaFree && this.createParams.fangkaFree > 0 && (tData.roundAll >= 2 * tData.roundNum || tData.roundAll >= 50)) {
            logger.info("玩法", tData.gameType, "进入----对局内玩家输赢调控");
            for (var i = 0; i < tData.maxPlayer; i++) {
                var pl = this.players[tData.uids[i]];
                if (pl) {
                    playersAllScore[pl.uid] = pl.winall;
                    if (maxUids.length == 0 || pl.winall > maxWinScore) {
                        maxUids = [pl.uid];
                        maxWinScore = pl.winall;
                    }
                    else if (pl.winall == maxWinScore) {
                        maxUids.push(pl.uid);
                    }
                    if (minUids.length == 0 || pl.winall < minWinScore) {
                        minUids = [pl.uid];
                        minWinScore = pl.winall;
                    }
                    else if (pl.winall == minWinScore) {
                        minUids.push(pl.uid);
                    }
                }
            }
            var maxWinUid = maxUids.length > 1 ? maxUids[Math.floor(maxUids.length * Math.random())] : maxUids[0];
            var minWinUid = minUids.length > 1 ? minUids[Math.floor(minUids.length * Math.random())] : minUids[0];
            var freeMulitiply = tData.freeFangKaConfig["免扣比例"] ? tData.freeFangKaConfig["免扣比例"] : 2;
            logger.debug("免扣倍率:", freeMulitiply, tData.gameType);
            if (tData.roundAll >= 50) {
                var temp = maxWinScore;
                //重新算免扣分数，邵阳剥皮玩法免扣算法：A-B+A-C
                maxWinScore = 0;
                for (var uid in playersAllScore) {
                    if (uid != maxWinUid) {
                        maxWinScore += playersAllScore[maxWinUid] - playersAllScore[uid];
                    }
                }
                if (maxWinScore <= freeMulitiply * this.createParams.fangkaFree && temp > 50) {
                    luckPlayer = maxWinUid;
                }
                else if (maxWinScore >= freeMulitiply * 2 * this.createParams.fangkaFree && temp > 50) {
                    luckPlayer = minWinUid;
                }
            }
            else {
                if (maxWinScore <= freeMulitiply * this.createParams.fangkaFree) {
                    luckPlayer = maxWinUid;
                }
                else if (maxWinScore >= freeMulitiply * 2 * this.createParams.fangkaFree) {
                    luckPlayer = minWinUid;
                }
            }
            if (luckPlayer) {
                if (tData.roundNum == 1 && maxWinScore < this.createParams.fangkaFree || !lastLuckyPlayer || luckPlayer != lastLuckyPlayer || this.players[luckPlayer].winone < maxWinone || tData.winner == -1) {
                    var randomValue = tData.freeFangKaConfig["调牌概率"] ? tData.freeFangKaConfig["调牌概率"] : 1;
                    if (Math.random() < randomValue) {
                        this.restartPutCards(luckPlayer, putCardsPattern);
                        tData.lastLuckyPlayer = luckPlayer;
                    }
                }
            }
        }
    }

    Table.prototype.restartPutCards = function (luckPlayer, pattern) {
        var tData = this.tData;
        var maxChangeNum = tData.freeFangKaConfig["换牌最大次数"] ? tData.freeFangKaConfig["换牌最大次数"] : 50;
        var initNum = maxChangeNum;
        var firstChange = true;
        var bestChangeCards = [];
        while (this.adjustPlayersScoreAnalyse(luckPlayer, firstChange, bestChangeCards, pattern) && maxChangeNum-- > 0) {
            this.cards = this.pGameCode.majiang.randomCards(tData.areaSelectMode, tData, this);
            if (!("cardNextTemp" in tData)) {
                logger.error("cardNextTemp不存在");
            }
            tData.cardNext = tData.cardNextTemp;
            if (this.pGameCode.randomHunCard) {
                tData.hunCard = this.pGameCode.randomHunCard(this.cards, tData);
            }
            logger.debug("第" + (initNum - maxChangeNum) + "次重新发牌");
            firstChange = false;
        }

        if (maxChangeNum <= 0) {
            this.cards = bestChangeCards[1];
            tData.otherCards = bestChangeCards[2];
            tData.showCardIndex = bestChangeCards[3];
            tData.showCard = bestChangeCards[4];
            tData.hunCard = bestChangeCards[5];
            logger.info("玩法ID", tData.gameType, "换牌次数用完，没有换牌成功,选用换牌分值最高的一次", JSON.stringify(bestChangeCards[1]));
        }
    }

    //计算最高分玩家手牌分是否最高
    Table.prototype.adjustPlayersScoreAnalyse = function (targetUid, firstChange, bestCards, pattern) {
        var tData = this.tData;
        var tCards = this.cards.slice();
        var handCount = this.pGameCode.majiang.handCount;
        if (tData.gameType == GAME_TYPE.SHAO_YANG_BO_PI && tData.maxPlayer == 4 && !tData.areaSelectMode.zuoXing) {
            handCount = 14;
        } else if (tData.gameType == GAME_TYPE.CHEN_ZHOU_ZI_PAI) {
            if (tData.maxPlayer == 4 || tData.areaSelectMode.isShiWuZhang) {
                handCount = 14;
            } else {
                handCount = 20;
            }
        }
        var spadeThree = false;
        var recordCards = {};
        var cardNext = tData.cardNext;
        var targetHandScore = -99;
        var otherHandMaxScore = -99;
        var otherHandMinScore = 99;
        for (var i = 0; i < tData.maxPlayer; i++) {
            var playerHands = [];
            for (var j = 0; j < handCount; j++) {
                var newCard = tCards[cardNext++];
                while (this.pGameCode.isHardFlower && this.pGameCode.isHardFlower(newCard, tData)) {
                    newCard = tCards[cardNext++];
                }
                playerHands.push(newCard);
            }
            playerHands.sort(function (a, b) { return a - b; });
            recordCards[tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]] = playerHands;
            //打印玩家原始手牌
            if (firstChange) {
                logger.info("桌子ID", tData.tableid, "玩法", tData.gameType, "局数", tData.roundNum, "目标玩家ID:", targetUid, "当前玩家ID:", tData.uids[i], "原始手牌:", JSON.stringify(playerHands));
            }
            //跑的快所需数据
            var pokerInfo = { myPos: i, target: targetUid, allHands: [playerHands, tCards.slice(handCount, handCount * 2), tCards.slice(handCount * 2, handCount * 3)] }
            var playerHandScore = this.pGameCode.calculateScore(playerHands, tData.areaSelectMode, pokerInfo, this);
            if (targetUid == tData.uids[(i + tData.zhuang + tData.maxPlayer) % tData.maxPlayer]) {
                targetHandScore = playerHandScore;
                //是否有黑桃三
                spadeThree = playerHands.indexOf(12) >= 0;
            } else {
                if (otherHandMaxScore < playerHandScore) {
                    otherHandMaxScore = playerHandScore;
                }
                if (otherHandMinScore > playerHandScore) {
                    otherHandMinScore = playerHandScore;
                }
            }
        }
        //评估手牌是否需要调牌
        logger.debug("目标手牌分:", targetHandScore, "其他玩家最大手牌分:", otherHandMaxScore, "其他玩家最小手牌分:", otherHandMinScore);

        //记录目前最好情况换牌
        if (pattern >= 0) {
            var value = targetHandScore - otherHandMaxScore;
        } else {
            var value = otherHandMinScore - targetHandScore;
        }
        if (!bestCards[1]) {
            bestCards[0] = value;
            bestCards[1] = this.cards;
            bestCards[2] = tData.otherCards;
            bestCards[3] = tData.showCardIndex;
            bestCards[4] = tData.showCard;
            bestCards[5] = tData.hunCard;
        } else if (value > bestCards[0]) {
            bestCards[0] = value;
            bestCards[1] = this.cards;
            bestCards[2] = tData.otherCards;
            bestCards[3] = tData.showCardIndex;
            bestCards[4] = tData.showCard;
            bestCards[5] = tData.hunCard;
        }
        var score = this.pGameCode.disparityScore(this, targetUid, spadeThree);
        logger.debug("牌值分差:", score);
        if (pattern >= 0 && targetHandScore < otherHandMaxScore + score) {
            return true;
        }
        if (pattern == -1 && targetHandScore + score > otherHandMinScore) {
            return true;
        }
        for (var playerUid in recordCards) {
            logger.info("桌子ID", tData.tableid, "玩法", tData.gameType, "局数", tData.roundNum, "目标玩家ID:", targetUid, "当前玩家ID:", playerUid, "换牌成功:", JSON.stringify(recordCards[playerUid]));
        }
        return false;
    }

    /**
     * 解散房间 old
     */
    Table.prototype.EndTable = function (disbander) {
        var uids = this.tData.uids;

        var disbanderInfo = '';
        if (disbander) {
            disbanderInfo = unescape(disbander.nickname) + '(' + disbander.uid + ')';
        }
        this.AllPlayerRun(function (p) {
            p.mjdesc.push('会长或管理员' + disbanderInfo + '于' + utils.dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss') + '解散房间');
        });
        this.RoomEnd({ reason: 3, yesuid: uids, disbander: disbander || {} });
        var disbandText = '';
        if (disbander) {
            disbandText = '该房间已被会长或管理员解散';
        }
        for (var i = 0; i < uids.length; i++) {
            app.rpc.pkplayer.Rpc.notification(app.GetServerById('pkplayer', uids[i]).id, uids[i], '房间通知', disbandText, emptyFunction);
        }
    };

    /**
     * 解散房间
     */
    Table.prototype.dissolve = function (reason, disbander) {
        this.RoomEnd({ reason: reason || 0, disbander: disbander || {} });
    };

    Table.prototype.showDissmissDesc = function () {
        var tData = this.tData;
        var self = this;
        this.AllPlayerRun(function (p) {
            if (p.uid == tData.firstDel) {
                p.mjdesc.splice(0, 0, " 于" + utils.dateFormat(new Date(tData.delEnd - self.createParams.voteTimeout * 60000), 'yyyy-MM-dd hh:mm:ss') + "申请解散");
            }
            else if (p.mjdesc.toString().indexOf('会长') > -1) {
                p.mjdesc.push("默认同意");
            }
            else if (p.delRoom > 0) {
                p.mjdesc.push("同意解散");
            }
            else if (p.delRoom < 0) {
                p.mjdesc.push("拒绝解散");
            }
            else if (utils.isContains(['js3mj', 'js3mj-test'], env) && !p.onLine) {
                p.mjdesc.push("离线默认同意");
            }
            else if (self.createParams.clubId && !p.onLine && Date.now() - p.lastOffLineTime > 5 * 60000) {
                p.mjdesc.push("超时默认同意");
            }
            else {
                p.mjdesc.push("默认同意");
            }
        });
    };

    Table.prototype.showDisHePaiDesc = function () {
        var tData = this.tData;
        this.AllPlayerRun(function (p) {
            if (p.uid == tData.firstDel) {
                p.mjdesc.splice(0, 0, " 于" + utils.dateFormat(new Date(tData.delEndHePai - 2 * 60000), 'yyyy-MM-dd hh:mm:ss') + "申请和牌");
            }
            else if (p.mjdesc.toString().indexOf('会长') > -1) {
                p.mjdesc.push("默认同意");
            }
            else if (p.delRoomHePai > 0) {
                p.mjdesc.push("同意和牌");
            }
            else if (p.delRoomHePai < 0) {
                p.mjdesc.push("拒绝和牌");
            }
            else {
                p.mjdesc.push("超时默认同意");
            }
        });
    };
    //第一局结束
    Table.prototype.firstRoundEndDo = function () {
        this.tData.isValidTable = true;
        if (this.createParams.round == 1 && this.tData.isDismiss) { // 一局场提前解散
            return;
        }
        if (this.createParams.money <= 0) {
            return
        }
        if (this.tData.hasPay) {
            return;
        }
        logger.debug("单局结束，打印创建房间信息", this.createParams);
        this.tData.hasPay = true;
        if (this.createParams.payWay == 0) {
            var payMoney = this.createParams.money;
            // 如果是自由人数
            if (this.createParams.convertible) {
                try {
                    var maxPlayerNum = this.extraParams.maxPlayer; // 自由人数开房时 按最大人数扣除元宝

                    // 泗阳和新泗阳自由人数开房 按实际开房人数扣费
                    if (this.tData.gameType == GAME_TYPE.SI_YANG || this.tData.gameType == GAME_TYPE.XIN_SI_YANG) {
                        maxPlayerNum = this.tData.maxPlayer;
                    }

                    payMoney = GAME_CFG.price[this.tData.gameType][maxPlayerNum][this.tData.roundAll][0];
                }
                catch (err) {
                    logger.error("扣费时取价格错误", this.tData.gameType, this.tData.maxPlayer, this.tData.roundAll);
                }
            }

            if (this.createParams.realtimeVoice && this.createParams.voice > 0) {
                payMoney += this.createParams.voice;
            }

            if (this.players[this.tData.owner]) {
                this.players[this.tData.owner].info.money -= payMoney;
                this.players[this.tData.owner].info.lockMoney -= payMoney;
                if (this.players[this.tData.owner].info.lockMoney < 0) this.players[this.tData.owner].info.lockMoney = 0;
            }
            if (this.tData.alreadyUnlockMoney) {
                app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', this.tData.owner).id, this.tData.owner, { $inc: { money: -payMoney } }, false, emptyFunction);
            }
            else {
                app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', this.tData.owner).id, this.tData.owner, { $inc: { money: -payMoney, lockMoney: -payMoney } }, false, emptyFunction);
            }
            modules.user.updateMoney({ userId: this.tData.owner, amount: -payMoney, clubId: this.createParams.clubId || 0, reason: this.tData.gameCnName + '房费(' + this.tableid + ')' });
        }
        if (this.createParams.payWay == 1) {
            var payMoney = this.createParams.money;
            if (this.createParams.realtimeVoice && this.createParams.voice > 0) {
                payMoney += this.createParams.voice;
            }

            //牛牛处理操作，牛牛小结算只考虑当前局在玩的人
            if(this.tData.gameType == GAME_TYPE.NIU_NIU || this.tData.gameType == GAME_TYPE.ZHA_JIN_HUA || this.tData.gameType == GAME_TYPE.LAO_YAN_CAI) {
                var maxPlayer = this.tData.rungingUids.length;
                for (var i = 0; i < maxPlayer; i++) {
                    var pl = this.getPlayer(this.tData.rungingUids[i]);
                    pl.info.money -= payMoney;
                    pl.info.lockMoney -= payMoney;
                    if(pl.info.lockMoney < 0 ) pl.info.lockMoney = 0;
                    app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', pl.uid).id, pl.uid, {$inc: {money: -this.createParams.money}}, false, emptyFunction);
                    modules.user.updateMoney({userId: pl.uid, amount: -this.createParams.money, reason: this.tData.gameCnName + '房费AA房(' + this.tableid + ')'});
                }
            } else {
                this.AllPlayerRun((pl) => {
                    pl.info.money -= payMoney;
                    pl.info.lockMoney -= payMoney;
                    if(pl.info.lockMoney < 0 ) pl.info.lockMoney = 0;
                    app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', pl.uid).id, pl.uid, {$inc: {money: -this.createParams.money}}, false, emptyFunction);
                    modules.user.updateMoney({userId: pl.uid, amount: -this.createParams.money, clubId: this.createParams.clubId || 0, reason: this.tData.gameCnName + '房费AA房(' + this.tableid + ')'});
                });
            }
        }
    };
    //每一局结束
    Table.prototype.perRoundEndDo = function () {
        var tb = this;
        var tData = this.tData;
        if (!this.tData.isValidTable) {
            return;
        }
        // 小数精度修正
        var difen = tData.areaSelectMode["difen"] || 1;
        this.AllPlayerRun(function (p) {
            p.winone = revise(p.winone);
            p.winall = revise(p.winall);
            if (tb.pGameCode.calGrade) {
                tb.pGameCode.calGrade(tb, p);
                p.info.grade.common = Math.max(0, Math.min(300, p.info.grade.common));
            }
        });

        if (this.createParams.trustTime && !this.createParams.isTrustWhole) { // 普通房间每局取消托管(没有选中整场托管)
            this.AllPlayerRun(function (p) {
                if (p.trust) {
                    tb.cancelTrust(p, null, null, function () { });
                }
            });
        }
        tData.delEnd = 0; // 取消定时结束
        tData.delEndHePai = 0;//取消和牌定时
        this.clearAllTrustTimer();
        let lossScore = 0;
        let winScore = 0;
        let isLess = false;
        let isWin = false;

        for (let uid in this.players) {
            let player = this.players[uid];
            if (utils.isEmptyObject(player)) {
                return;
            }
            let score = player.info.happybean + player.winall - player.winone; //上局的积分
            if (player.winone < 0) {
                let rateScore = (tb.createParams.xiPaiLimitScore * player.shuffleNum) + (tb.createParams.anCost ? tb.createParams.anRate : 0);
                score = score - rateScore;
                if (score < (0 - player.winone)) {
                    lossScore += score;
                    isLess = true;
                } else {
                    lossScore -= player.winone;
                }
            } else {
                if (player.info.happybean < player.winall) {
                    winScore = player.winall - player.info.happybean;
                    isWin = true;
                }
            }
        }

        if (isLess && isWin) {
            if (lossScore < winScore) {
                isLess = false;
            } else {
                isWin = false;
            }
        }

        if (isLess) {
            winScore = lossScore;
            for (let uid in this.players) {
                let player = this.players[uid];
                if (utils.isEmptyObject(player)) {
                    return;
                }

                if (player.winone > 0) {
                    if (player.winone > winScore) {
                        player.winall = player.winall - player.winone + winScore;
                        player.winone = winScore;
                        winScore = 0;
                    } else {
                        winScore -= player.winone;
                        player.winone = 0;
                    }
                } else if (player.winone < 0) {
                    let tmpwinone = 0 - player.winone;
                    let score = player.info.happybean + player.winall - player.winone; //上局的积分
                    let rateScore = (tb.createParams.xiPaiLimitScore * player.shuffleNum) + (tb.createParams.anCost ? tb.createParams.anRate : 0);
                    score = score - rateScore;
                    if (score < tmpwinone) {
                        if (score <= lossScore) {
                            player.winall = player.winall - player.winone - score;
                            player.winone = 0 - score;
                        }
                    }
                }
            }
        }

        if (isWin) {
            lossScore = winScore;
            for (let uid in this.players) {
                let player = this.players[uid];
                if (utils.isEmptyObject(player)) {
                    return;
                }

                if (player.winone > 0) {
                    player.winall = player.info.happybean;
                    player.winone = player.winone - winScore;
                } else if (player.winone < 0) {
                    if (-player.winone > lossScore) {
                        player.winall = player.winall + lossScore;
                        player.winone = player.winone + lossScore;
                        lossScore = 0;
                    } else {
                        lossScore += player.winone;
                        player.winall = player.winall - player.winone;
                        player.winone = 0;
                    }
                }
            }
        }
        // 统计小结算数据
        this.AllPlayerRun(function (p) {
            if (utils.isEmptyObject(tData.winOneData)) {
                tData.winOneData = {};
            }
            if (utils.isEmptyObject(tData.winOneData[p.uid])) {
                tData.winOneData[p.uid] = [];
            }
            tData.winOneData[p.uid].push(p.winone);

            if (p.info.type == 4) { //机器人处理  每局结束机器人自动准备                          
                //p.mjState = TableState.isReady;  
                if (p.info.type == 4) {
                    if (p.robotReadyTime != null) {
                        clearTimeout(p.robotReadyTime);
                        p.robotReadyTime = null;
                    }
                    p.robotReadyTime = setTimeout(function () {
                        if (tb.pGameCode.mjPassCard) {
                            tb.pGameCode.mjPassCard(p, { eatFlag: 0 }, null, emptyFunction, tb);
                        } else if (tb.pGameCode.pkPassCard) {
                            tb.pGameCode.pkPassCard(p, { eatFlag: 0 }, null, emptyFunction, tb);
                        }
                    }, Math.floor(Math.random(1000) + 2001)); //机器人处理 自动准备 随机1~2秒
                }
            }
        });

        //tData.roundNum = tData.roundAll-tData.winOneData[tData.uids[0]].length;

        // 防沉迷低分解散逻辑
        if (tb.createParams.isAntiAddictionDissolveLimit) {
            var limitScore = tb.createParams.antiAddictionDissolveLimitScore || 0;
            tData.antiAddictionScoreLimitUser = {};
            for (let uid in this.players) {
                let player = this.players[uid];
                if (utils.isEmptyObject(player)) {
                    return;
                }
                var score = player.info.happybean + player.winall;
                if (player.winall < 0) {
                    let rateScore = (tb.createParams.xiPaiLimitScore * player.shuffleNum) + (tb.createParams.anCost ? tb.createParams.anRate : 0);
                    score = score - rateScore;
                }
                if (score <= limitScore || player.info.happybean <= player.winall) {
                    //if (score <= limitScore) {
                    tData.antiAddictionScoreLimitUser[player.uid] = { score: score, nickname: player.info.nickname };
                }
                logger.debug("防沉迷低分解散逻辑：", limitScore, score, player.info.happybean, player.winall);
            }
            if (!utils.isEmptyObject(tData.antiAddictionScoreLimitUser)) {
                this.createParams.dissolveWay = 6; // 解散方式:低分解散
                this.createParams.antiAddictionScoreLimitUser = tData.antiAddictionScoreLimitUser;
                if (tData.roundNum > 1) {
                    setTimeout(() => { // 需要执行完小结算，再解散
                        this.dissolve(7); // 低分解散
                    }, 1);
                }
            }
        }
    };
    // 至少打了一局的房间结束了 直接解散也调这里
    Table.prototype.validTableEndDo = function () {
        var min = 0;//当前最小分
        var max = 0;//当前最大分
        var loser = 0;//输家
        var winner = 0;//赢家
        var players = {};//临时表
        var rebate = 0;//钻石返利比例
        var receivable = 0;//应收钻石数
        var winnerList = [];//赢家列表
        var loserList = [];//输家列表
        var roundNum = this.tData.roundNumPre || this.tData.roundNum;
        this.cancelTrustWholeAction();
        if (this.pGameCode.calculateScore) {
            logger.info("降低免扣功能记录数据分析:" + JSON.stringify(this.changeCardRecord));
        }
        if (utils.isContains([GAME_TYPE.LOU_DI_FANG_PAO_FA, GAME_TYPE.XIANG_XIANG_GAO_HU_ZI, GAME_TYPE.SHAO_YANG_FANG_PAO_FA, GAME_TYPE.HENG_YANG_FANG_PAO_FA, GAME_TYPE.LV_LIANG_MA_JIANG], this.createParams.gameType) && this.tData.tempNum) {
            roundNum = this.tData.tempNum;
        }
        if (roundNum < 0) {
            roundNum = 0;
        }
        if (this.tData.extraNum) {
            roundNum -= this.tData.extraNum;
        }

        if (this.createParams.clubId) {
            app.rpc.pkclub.Rpc.refreshRoom(pkclubDispatcher.dispatch(this.createParams.clubId), this.createParams.clubId, 'end', { ruleId: this.createParams.ruleId, roomNum: this.tableid }, emptyFunction);
        }
        if (!this.tData.isValidTable && !utils.isContains([GAME_TYPE.SAN_DA_HA, GAME_TYPE.YUE_YANG_SAN_DA_HA], this.createParams.gameType)) {
            return;
        }
        this.createParams.dissmissDesc = {};
        this.createParams.lastOffLineTime = {}; // 解散时间
        this.AllPlayerRun((p) => {
            if (p.uid == this.tData.firstDel) {
                var firstDelTime = this.tData.firstDelTime || 0;
                this.createParams.dissmissDesc[p.uid] = "于" + utils.dateFormat(new Date(firstDelTime - this.createParams.voteTimeout * 60000), 'yyyy-MM-dd hh:mm:ss') + "申请解散";
            }
            else if (p.mjdesc.toString().indexOf('会长') > -1) {
                this.createParams.dissmissDesc[p.uid] = "默认同意";
            }
            else if (p.delRoom > 0) {
                this.createParams.dissmissDesc[p.uid] = "同意解散";
            }
            else if (p.delRoom < 0) {
                this.createParams.dissmissDesc[p.uid] = "拒绝解散";
            }
            else if (this.createParams.clubId && !p.onLine && Date.now() - p.lastOffLineTime > 5 * 60000) {
                this.createParams.dissmissDesc[p.uid] = "超时默认同意";
            }
            else {
                this.createParams.dissmissDesc[p.uid] = "默认同意";
            }
            players[p.uid] = {
                winall: p.winall,
                nickname: p.info.nickname,
                clubId: p.info.clubId,
                happybean: p.info.happybean,
                partnerTree: p.info.parentPartnerTree,
                roleId: p.info.roleId,
                shuffleNum: p.shuffleNum
            };
            if (p.lastOffLineTime) {
                players[p.uid].lastOffLineTime = p.lastOffLineTime;
                this.createParams.lastOffLineTime[p.uid] = p.lastOffLineTime;
            }
            if (utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.LONG_HUI_BA_ZHA_DAN], this.createParams.gameType)) {
                players[p.uid].winall = p.windif;
                players[p.uid].score_spclType = p.score_spclType;
                players[p.uid].score_lastRound = p.score_lastRound;
                players[p.uid].windif = p.windif;
                if (p.teamid) {
                    players[p.uid].teamid = p.teamid;
                    players[p.uid].t_windif = this.teams[p.teamid].windif;
                    players[p.uid].winall = this.teams[p.teamid].windif;
                    players[p.uid].t_score_total = this.teams[p.teamid].winall + this.teams[p.teamid].score_lastRound + this.teams[p.teamid].score_spclType;
                }
            }
            if (utils.isContains([GAME_TYPE.DIAN_TUO], this.createParams.gameType)) {
                players[p.uid].score_xi = p.score_xi;
                players[p.uid].score_xi_all = p.score_xi_all;
            }
            if (utils.isContains([GAME_TYPE.DA_ZI_BO_PI], this.createParams.gameType)) {
                players[p.uid].winall = p.winScore;
            }
            if (utils.isContains([GAME_TYPE.SHAO_YANG_BO_PI], this.createParams.gameType)) {
                players[p.uid].winall = p.winScore2;
            }
            //大结算翻倍分数
            if (p.winall2 != undefined) {
                players[p.uid].winall = p.winall2;
            }
            if (utils.isContains([GAME_TYPE.XIANG_XIANG_GAO_HU_ZI, GAME_TYPE.LOU_DI_FANG_PAO_FA, GAME_TYPE.HENG_YANG_FANG_PAO_FA], this.createParams.gameType)) {
                players[p.uid].winall = p.fenshu;
            }
            if (utils.isContains([GAME_TYPE.SHAO_YANG_FANG_PAO_FA], this.createParams.gameType)) {
                players[p.uid].winall = p.fenshu2;
            }
            if (utils.isContains([GAME_TYPE.NAN_JING], this.createParams.gameType) && this.createParams.playType == 0) {
                players[p.uid].winall -= 100;
            }
            if (players[p.uid].winall < min) {
                min = players[p.uid].winall;
                loser = p.uid;
            }
            if (players[p.uid].winall > max) {
                max = players[p.uid].winall;
                winner = p.uid;
            }
        });
        var addUserRankScore = this.gameTypeScoreRank != null ? this.gameTypeScoreRank.addUserScore : [];
        if (!this.tData.isValidTable) {
            if (this.mjlog.length > 1) {
                this.createParams.money = 0;
                modules.user.game({
                    clubId: this.createParams.clubId || 0,
                    ruleId: this.createParams.ruleId,
                    creator: this.createParams.owner,
                    winner: winner,
                    gameType: this.createParams.gameType,
                    gameInfo: this.createParams,
                    extraParams: this.extraParams,
                    maxPlayer: this.createParams.maxPlayer,
                    payWay: this.createParams.payWay,
                    cost: this.createParams.money || 0,
                    roomNum: this.tableid,
                    roundNum: this.createParams.round,
                    roundBeen: this.tData.roundAll - roundNum,
                    gameCnName: this.tData.gameCnName,
                    createTime: this.createTime,
                    startTime: this.startTime,
                    endTime: Date.now(),
                    extraTimeVote: this.tData.extraTimeVote,
                    winOneData: this.tData.winOneData,
                    addUserRankScore: addUserRankScore
                }, players);
            }
            return;
        }
        var isOneRoundDisss = this.createParams.round == 1 && this.tData.isDismiss; // 一局场提前解散
        if (isOneRoundDisss) {  // 一局提前解散，元宝清除
            this.createParams.money = 0;
        }

        if (this.createParams.payWay == 2 && this.createParams.money > 0) {
            var payList = [];
            for (var uid in players) {
                if (players[uid].winall == max) {
                    payList.push(uid);
                }
            }
            var payCount = Math.ceil(this.createParams.money / payList.length);
            if (this.createParams.convertible) {
                try {
                    payCount = Math.ceil(GAME_CFG.price[this.tData.gameType][this.tData.maxPlayer][this.tData.roundAll][2] / payList.length);
                }
                catch (err) {
                    payCount = this.createParams.money;
                }
            }
            for (var i = 0; i < payList.length; i++) {
                app.rpc.pkplayer.Rpc.updateUser(app.GetServerById('pkplayer', payList[i]).id, payList[i], { $inc: { money: -payCount } }, false, emptyFunction);
                modules.user.updateMoney({ userId: payList[i], amount: -payCount, clubId: this.createParams.clubId || 0, reason: this.tData.gameCnName + '大赢家房费(' + this.tableid + ')' });
            }
        }

        if (!utils.isStrNotEmpty(this.createParams.payWay)) {
            logger.error('缺少必要参数 payWay', 'gameType:' + this.createParams.gameType, 'clubId:' + this.createParams.clubId);
        }
        modules.user.game({
            clubId: this.createParams.clubId || 0,
            ruleId: this.createParams.ruleId,
            creator: this.createParams.owner,
            winner: winner,
            gameType: this.createParams.gameType,
            gameInfo: this.createParams,
            extraParams: this.extraParams,
            maxPlayer: this.createParams.maxPlayer,
            payWay: this.createParams.payWay,
            cost: this.createParams.money || 0,
            roomNum: this.tableid,
            roundNum: this.createParams.round,
            roundBeen: this.tData.roundAll - roundNum,
            gameCnName: this.tData.gameCnName,
            createTime: this.createTime,
            startTime: this.startTime,
            endTime: Date.now(),
            extraTimeVote: this.tData.extraTimeVote,
            winOneData: this.tData.winOneData,
            addUserRankScore: addUserRankScore
        }, players, (recordId) => {
            if (this.createParams.money == 0) {
                return;
            }
        });
    };
    //清除所有托管倒计时定时器
    Table.prototype.clearAllTrustTimer = function () {
        this.AllPlayerRun(function (p) {
            p.clearTrustTimer();
        });
    };

    // 托管出牌
    Table.prototype.trustAction = function (pl) {
        if (pl.actionTimer) {
            clearTimeout(pl.actionTimer);
        }

        var trustActionDelay = 700;
        if (this.pGameCode.trustActionDelay)
            trustActionDelay = this.pGameCode.trustActionDelay(this);

        pl.actionTimer = setTimeout(() => {
            var targetUid = this.tData.uids[parseInt(Math.random() * this.tData.uids.length)];
            if (pl.info.type == 4 && pl.uid != targetUid && Math.random() < 0.10) {
                this.NotifyAll('MJFight', { cmd: 'MJFight', uid: pl.uid, targetUid: targetUid, kind: parseInt(Math.random() * 6) });
            }
            if (this.pGameCode.trustPutCard) {
                this.pGameCode.trustPutCard(pl, this);
                return;
            }
            if (this.pGameCode && this.pGameCode.majiang && this.pGameCode.majiang.tipCards && pl.mjState == TableState.waitPut) {
                // 牌类托管
                var nextPlayer = this.getPlayer(this.tData.uids[(this.tData.maxPlayer + this.tData.curPlayer + 1) % this.tData.maxPlayer]);
                var isNextPlayerOneCard = nextPlayer.handCount == 1;
                var isFirstRound = this.tData.roundAll == this.tData.roundNum;
                var tipscards = this.pGameCode.majiang.tipCards(pl.mjhand, this.tData.lastPutCard, this.tData.areaSelectMode, isNextPlayerOneCard, isFirstRound, true);
                if (tipscards && tipscards.length > 0) {
                    this.pGameCode.pkPutCard(pl, { cmd: "PKPut", card: tipscards[0] }, null, null, this)
                } else {
                    this.pGameCode.pkPassCard(pl, null, null, null, this);
                }
            } else {
                // 麻将托管
                if (pl.eatFlag & 8) {
                    this.pGameCode.mjHuCard(pl, { eatFlag: pl.eatFlag }, null, null, this);
                } else if (pl.mjState == TableState.waitPut && pl.uid == this.tData.uids[this.tData.curPlayer]) {
                    for (var i = pl.mjhand.length - 1; i >= 0; i--) {
                        var card = pl.mjhand[i];
                        if (card != this.tData.hunCard) {
                            break;
                        }
                    }
                    this.pGameCode.mjPutCard(pl, { cmd: "MJPut", card: card }, null, null, this);
                } else if (pl.mjState == TableState.waitEat) {
                    this.pGameCode.mjPassCard(pl, { eatFlag: pl.eatFlag }, null, null, this);
                } else if (pl.haiDiLaoState == 2) {
                    this.pGameCode.mjSelectHaiDiLao(pl, { haiDiLaoState: 0 }, null, null, this);
                } else {
                    logger.debug("uid:%d 托管未出牌", pl.uid);
                }
            }
        }, trustActionDelay);
    };


    // 机器人处理 机器人打牌操作统一处理实现
    Table.prototype.robotAction = function (pl) {

        var delay = Math.floor(Math.random() * 500) + 900;

        if (pl.putCount == 0 || (pl.table.tData.gameType == GAME_TYPE.PAO_DE_KUAI_TY && pl.table.tData.lastPutPlayer == -1)) {
            delay += 2000;
        }

        setTimeout(() => {
            //机器人处理丢道具
            //var targetUid = this.tData.uids[parseInt(Math.random() * this.tData.uids.length)];
            // if (pl.info.type == 4 && pl.uid != targetUid && Math.random() < 0.10) { 
            //     this.NotifyAll('MJFight', {cmd: 'MJFight', uid: pl.uid, targetUid: targetUid, kind: parseInt(Math.random() * 6)});
            // }

            //机器人处理 打牌处理逻辑接口
            if (this.pGameCode.robotPutCard) {
                this.pGameCode.robotPutCard(pl, this);
                return;
            }

            if (this.pGameCode.trustPutCard) {
                this.pGameCode.trustPutCard(pl, this);
                return;
            }

            if (this.pGameCode && this.pGameCode.majiang && this.pGameCode.majiang.tipCards && pl.mjState == TableState.waitPut) {
                // 机器人处理 牌类基础操作
                var nextPlayer = this.getPlayer(this.tData.uids[(this.tData.maxPlayer + this.tData.curPlayer + 1) % this.tData.maxPlayer]);
                var isNextPlayerOneCard = nextPlayer.handCount == 1;
                var isFirstRound = this.tData.roundAll == this.tData.roundNum;
                var tipscards = this.pGameCode.majiang.tipCards(pl.mjhand, this.tData.lastPutCard, this.tData.areaSelectMode, isNextPlayerOneCard, isFirstRound, true);
                if (tipscards && tipscards.length > 0) {
                    this.pGameCode.pkPutCard(pl, { cmd: "PKPut", card: tipscards[0] }, null, null, this)
                } else {
                    this.pGameCode.pkPassCard(pl, null, null, null, this);
                }
            } else {
                // 机器人处理 麻将基础操作
                if (pl.eatFlag & 8) {
                    this.pGameCode.mjHuCard(pl, { eatFlag: pl.eatFlag }, null, null, this);
                } else if (pl.mjState == TableState.waitPut && pl.uid == this.tData.uids[this.tData.curPlayer]) {
                    for (var i = pl.mjhand.length - 1; i >= 0; i--) {
                        var card = pl.mjhand[i];
                        if (card != this.tData.hunCard) {
                            break;
                        }
                    }
                    this.pGameCode.mjPutCard(pl, { cmd: "MJPut", card: card }, null, null, this);
                } else if (pl.mjState == TableState.waitEat) {
                    this.pGameCode.mjPassCard(pl, { eatFlag: pl.eatFlag }, null, null, this);
                } else if (pl.haiDiLaoState == 2) {
                    this.pGameCode.mjSelectHaiDiLao(pl, { haiDiLaoState: 0 }, null, null, this);
                } else {
                    logger.error("机器人uid:%d 未出牌", pl.uid);
                }
            }
        }, delay);
    };

    // 托管倒计时
    Table.prototype.trustCountdown = function (pl) {
        if (pl.trust || (pl.mjState != TableState.waitEat && pl.mjState != TableState.waitJiazhu && pl.haiDiLaoState != 2 && pl.uid != this.tData.uids[this.tData.curPlayer])) {
            // 状态不对不需要倒计时
            return;
        }
        var countdown = 30;
        var tipCountdown = 30;
        if (this.createParams.trustTime) {
            countdown = this.createParams.trustTime + 1;
        }
        if (pl.trustTip) {
            clearTimeout(pl.trustTip);
        }
        if (countdown > tipCountdown) {
            pl.trustTip = setTimeout(() => {
                pl.trustTip = null;
                if (!pl.trust && (pl.mjState == TableState.waitEat || pl.mjState == TableState.waitJiazhu || pl.haiDiLaoState == 2 || pl.uid == this.tData.uids[this.tData.curPlayer])) {
                    this.NotifyAll("trustTip", { tipCountDown: tipCountdown, uid: pl.uid });
                }
            }, (countdown - tipCountdown) * 1000);
        }
        if (pl.trustTimer) {
            clearTimeout(pl.trustTimer);
        }
        pl.trustTimer = setTimeout(() => {
            pl.trustTimer = null;
            // 是否牌类托管
            if (this.pGameCode.beTrustLogic) { // 斗地主托管逻辑和通用的有差别，在子类实现
                this.pGameCode.beTrustLogic(pl, this);
                return;
            }
            if (!pl.trust && (pl.mjState == TableState.waitEat || pl.mjState == TableState.waitJiazhu || pl.haiDiLaoState == 2 || pl.uid == this.tData.uids[this.tData.curPlayer])) {
                pl.trust = true;
                var msg = { uid: pl.uid };
                this.NotifyAll("beTrust", msg);
                cloneDataAndPush(this.mjlog, "beTrust", msg);
                this.trustAction(pl);
            }
        }, countdown * 1000);
    };

    // 托管
    Table.prototype.doTrust = function (pl) {
        if (this.createParams.trustTime > 0) {
            if (pl.trust) {
                this.trustAction(pl);
            } else {
                this.trustCountdown(pl);
            }
        }

        if (pl.info.type == 4) {  //机器人处理 机器人打牌操作统一处理
            this.robotAction(pl);
        }
    };

    //全局托管
    Table.prototype.trustWhole = function (player) {
        if (this.createParams.trustTime <= 0) {
            return;
        }
        if (!this.createParams.isTrustWhole) {
            return;
        }
        if (this.tData.roundNum == 0) {
            return;
        }
        var self = this;
        this.AllPlayerRun(function (pl) {
            if (self.pGameCode.checkTrustWholeStatus) {
                if (!self.pGameCode.checkTrustWholeStatus(pl, self)) {
                    return;
                }
            } else if (pl.mjState != TableState.roundFinish) {
                return;
            }

            // 如已经设置了托管计时，则不用再设
            if (pl.trustWholeAction)
                return;

            // 使全局托管延迟可控
            var trustTime = 5;
            if (self.pGameCode.trustWholeDelay)
                trustTime = self.pGameCode.trustWholeDelay(self);

            if (!pl.trust) {
                trustTime = self.createParams.trustTime;
            }
            if (!player || player.uid == pl.uid) {
                pl.trustWholeAction = setTimeout(function () {
                    if (self.pGameCode.trustWholeAction) {
                        self.pGameCode.trustWholeAction(pl, self);
                    } else {
                        var cardNext = self.tData.cardNext;
                        self.GameReady(pl, { isTrust: true, cardNext: cardNext }, null, function () { });
                    }
                }, trustTime * 1000);
            }
        });
    };

    //取消全局托管
    Table.prototype.cancelTrustWholeAction = function (pl) {
        this.AllPlayerRun(function (p) {
            if (!pl || p.uid == pl.uid) {
                if (p.trustWholeAction) {
                    clearTimeout(p.trustWholeAction);
                    p.trustWholeAction = null;
                }
            }
        });
    };

    //小结算界面全局托管设置玩家托管状态(isTrust:是否是系统发起的准备状态)
    Table.prototype.updateTrustWholeStatus = function (pl, isTrust) {
        if (this.createParams.trustTime <= 0) {
            return;
        }
        if (!this.createParams.isTrustWhole) {
            return;
        }
        if (this.pGameCode.checkTrustWholeStatus) {
            if (!this.pGameCode.checkTrustWholeStatus(pl, this)) {
                return;
            }
        } else if (pl.mjState != TableState.roundFinish) {
            return;
        }

        var msg = null, cmd = null;
        if (isTrust) {
            pl.trust = true;
            cmd = "beTrust";
            msg = { uid: pl.uid, isTrust: true };
            this.NotifyAll(cmd, msg);
        } else if (!isTrust && pl.trust) {
            pl.trust = false;
            cmd = "cancelTrust";
            msg = { uid: pl.uid, isTrust: true };
            this.NotifyAll(cmd, msg);
        }
        //第一局加注、加锤、切牌自动托管日志处理
        this.trustWholeLog = null;
        if (this.tData.roundNum == this.tData.roundAll && msg != null) {
            this.trustWholeLog = [];
            cloneDataAndPush(this.trustWholeLog, cmd, msg);
        } else if (msg != null) {
            cloneDataAndPush(this.mjlog, cmd, msg);
        }
    };

    // 比赛场手牌优化
    Table.prototype.magicHands = function () {
        var _this = this;
        var tData = this.tData;
        this.AllPlayerRun(function (p) {
            var singleCards = [];
            var cardCounts = {};
            for (var i = 0; i < p.mjhand.length; i++) {
                increaseByKey(cardCounts, p.mjhand[i]);
            }
            for (var card in cardCounts) {
                card = Number(card);
                if (card == tData.hunCard) continue;
                if (cardCounts[card] == 1) {
                    if (cardCounts[card + 1] || cardCounts[card - 1] || card % 10 < 8 && cardCounts[card + 2] || card % 10 > 2 && cardCounts[card - 2]) {
                        // 有顺子
                    }
                    else {
                        singleCards.push(card);
                    }
                }
            }
            var leftCounts = {};
            for (var i = tData.cardNext; i < _this.cards.length; i++) {
                increaseByKey(leftCounts, _this.cards[i]);
            }
            // 交换手牌与剩余牌堆的牌
            var changeHandCard = function (oldCard, newCard) {
                _this.cards.splice(_this.cards.lastIndexOf(newCard), 1, oldCard);
                p.mjhand.splice(p.mjhand.indexOf(oldCard), 1, newCard);
                increaseByKey(leftCounts, oldCard);
                leftCounts[newCard]--;
            };
            var findKe = false;
            if (singleCards.length >= 3 && Math.random() < 0.4) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card] >= 3) { // 找到刻子
                        for (var j = 0; j < 3; j++) {
                            changeHandCard(singleCards.pop(), card);
                        }
                        findKe = true;
                        break;
                    }
                }
            }
            var findShun = false;
            if (singleCards.length >= 3 && Math.random() < 0.3) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card - 1] && leftCounts[card + 1]) { // 找到顺子
                        for (var j = -1; j <= 1; j++) {
                            changeHandCard(singleCards.pop(), card + j);
                        }
                        findShun = true;
                        break;
                    }
                }
            }
            if (!(findKe && findShun) && singleCards.length >= 2 && Math.random() < 0.4) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card] >= 2) { // 找到对子
                        for (var j = 0; j < 2; j++) {
                            changeHandCard(singleCards.pop(), card);
                        }
                        break;
                    }
                }
            }
        });
    };

    // 比赛场手牌优化配置实例rules:{71:[{level:150, rate:0.8}, {level:100, rate:0.5}, {level:50, rate:0.2}]}
    Table.prototype.luckyPlayer = function (rules, pl) {
        var tData = this.tData;
        var _this = this;
        if (!pl) {
            this.AllPlayerRun(function (p) {
                if (p.info.grade.common) {
                    if (!pl || pl.info.grade.common < p.info.grade.common) {
                        pl = p;
                    }
                }
            });
        }
        if (!pl) {
            return;
        }
        var singleCards = [];
        var cardCounts = {};
        for (var i = 0; i < pl.mjhand.length; i++) {
            increaseByKey(cardCounts, pl.mjhand[i]);
        }
        for (var card in cardCounts) {
            card = Number(card);
            if (card == tData.hunCard) continue;
            if (cardCounts[card] == 1) {
                if (cardCounts[card + 1] || cardCounts[card - 1] || card % 10 < 8 && cardCounts[card + 2] || card % 10 > 2 && cardCounts[card - 2]) {
                    // 有顺子
                }
                else {
                    singleCards.push(card);
                }
            }
        }
        var leftCounts = {};
        for (var i = tData.cardNext; i < _this.cards.length; i++) {
            increaseByKey(leftCounts, _this.cards[i]);
        }
        var changeHandCard = function (oldCard, newCard) {
            _this.cards.splice(_this.cards.lastIndexOf(newCard), 1, oldCard);
            pl.mjhand.splice(pl.mjhand.indexOf(oldCard), 1, newCard);
            increaseByKey(leftCounts, oldCard);
            leftCounts[newCard]--;
        };
        var isNeed = function (condition) {
            for (var i = 0; i < condition.length; i++) {
                if (pl.info.grade.common >= condition[i].level && Math.random() < condition[i].rate) {
                    return true;
                }
            }
            return false;
        };
        if (rules[71] && singleCards.length >= 1) {
            var rule = rules[71];
            if (isNeed(rule.condition)) {
                var find71 = false;
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    if (_this.cards[i] == 71) {
                        var singleCard = singleCards.pop();
                        logger.debug("*****++++ 拿牌堆里红中", singleCard, "换", 71);
                        changeHandCard(singleCard, _this.cards[i]);
                        pl.info.grade.common -= rule.cost || 0;
                        find71 = true;
                        break;
                    }
                }
                if (!find71) {
                    for (var i = 0; i < tData.maxPlayer; i++) {
                        var p = this.players[tData.uids[i]];
                        if (p == pl) {
                            continue;
                        }
                        var index71 = p.mjhand.indexOf(71);
                        if (index71 != p.mjhand.lastIndexOf(71)) {
                            var singleCard = singleCards.pop();
                            logger.debug("*****++++ 拿别人红中", singleCard, "换", 71);
                            p.mjhand.splice(index71, 1, singleCard);
                            pl.mjhand.splice(pl.mjhand.indexOf(singleCard), 1, 71);
                            pl.info.grade.common -= rule.cost || 0;
                            break;
                        }
                    }
                }
            }
        }
        if (rules["duizi2"] && singleCards.length == 2) {
            var rule = rules["duizi2"];
            if (isNeed(rule.condition)) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card] >= 2) { // 找到对子
                        logger.debug("*****++++ 拿牌堆对2子", singleCards[singleCards.length - 1], singleCards[singleCards.length - 2], "换一对", card);
                        for (var j = 0; j < 2; j++) {
                            changeHandCard(singleCards.pop(), card);
                        }
                        pl.info.grade.common -= rule.cost || 0;
                        if (rules.once) {
                            return;
                        }
                        break;
                    }
                }
            }
        }
        if (rules["shunzi"] && singleCards.length >= 3) {
            var rule = rules["shunzi"];
            if (isNeed(rule.condition)) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card - 1] > 0 && leftCounts[card + 1] > 0) { // 找到顺子
                        logger.debug("*****++++ 拿牌堆顺子", singleCards[singleCards.length - 1], singleCards[singleCards.length - 2], singleCards[singleCards.length - 3], "换顺子", card - 1, card, card + 1);
                        for (var j = -1; j <= 1; j++) {
                            changeHandCard(singleCards.pop(), card + j);
                        }
                        pl.info.grade.common -= rule.cost || 0;
                        if (rules.once) {
                            return;
                        }
                        break;
                    }
                }
            }
        }
        if (rules["kezi"] && singleCards.length >= 3) {
            var rule = rules["kezi"];
            if (isNeed(rule.condition)) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card] >= 3) {
                        logger.debug("*****++++ 拿牌堆刻子", singleCards[singleCards.length - 1], singleCards[singleCards.length - 2], singleCards[singleCards.length - 3], "换三张", card);
                        for (var j = 0; j < 3; j++) {
                            changeHandCard(singleCards.pop(), card);
                        }
                        pl.info.grade.common -= rule.cost || 0;
                        if (rules.once) {
                            return;
                        }
                        break;
                    }
                }
            }
        }
        if (rules["duizi"] && singleCards.length > 2) {
            var rule = rules["duizi"];
            if (isNeed(rule.condition)) {
                for (var i = _this.cards.length - 1; i >= tData.cardNext; i--) {
                    var card = _this.cards[i];
                    if (leftCounts[card] >= 2) { // 找到对子
                        logger.debug("*****++++ 拿牌堆对子", singleCards[singleCards.length - 1], singleCards[singleCards.length - 2], "换一对", card);
                        for (var j = 0; j < 2; j++) {
                            changeHandCard(singleCards.pop(), card);
                        }
                        pl.info.grade.common -= rule.cost || 0;
                        if (rules.once) {
                            return;
                        }
                        break;
                    }
                }
            }
        }
    };
    // 加时赛检查
    Table.prototype.checkExtraTime = function(dissolve) {
        if (dissolve) {
            return;
        }
        if (this.tData.roundNum > 0) {
            return;
        }
        if (this.tData.extraNum && this.tData.extraNum >= 2) {
            return;
        }
        if (this.createParams.isFangkaFreeTime) { // 免费场不会触发加时赛
            return;
        }
        if (!this.createParams.clubId && !this.createParams.leagueId) {
            return;
        }
        if (!this.createParams.fangkaFree || this.createParams.fangkaFree <= 0 || this.createParams.round == 1) { // 一局没有低分免扣也没有加时赛
            return;
        }

        if (!utils.isEmptyObject(this.tData.matchScoreLimitUser)) { // 比赛场低分解散不触发加时赛
            return;
        }

        logger.debug('checkExtraTime activityRedPacketOpen,activityExtraTimeOpen====', server.activityRedPacketOpen, server.activityExtraTimeOpen);
        this.tData.extraTimeType = 0;
        if (server.activityRedPacketOpen) { // 红包加时赛
            this.tData.extraTimeType = 1;
        } else if (server.activityExtraTimeOpen) { // 红包加时赛没开，普通加时赛开了
            if (!utils.isEmptyObject(server.extraTimeGameTypeMap) &&  utils.isStrNotEmpty(server.extraTimeGameTypeMap[this.tData.gameType])) {
                this.tData.extraTimeType = 2;
            }
        }
        if (this.tData.extraTimeType <= 0) {
            return;
        }

        var max = 0;
        this.AllPlayerRun((p) => {
            var winall = p.winall;
            if (utils.isContains([GAME_TYPE.DA_TONG_ZI_SHAO_YANG, GAME_TYPE.LONG_HUI_BA_ZHA_DAN], this.createParams.gameType)) {
                winall = !!p.teamid ? this.teams[p.teamid].windif : p.windif;
            }
            if (utils.isContains([GAME_TYPE.SHAO_YANG_BO_PI], this.createParams.gameType)) {
                winall = p.winScore;
            }
            if (utils.isContains([GAME_TYPE.XIANG_XIANG_GAO_HU_ZI, GAME_TYPE.LOU_DI_FANG_PAO_FA, GAME_TYPE.SHAO_YANG_FANG_PAO_FA, GAME_TYPE.HENG_YANG_FANG_PAO_FA], this.createParams.gameType)) {
                winall = p.fenshu;
            }
            if (utils.isContains([GAME_TYPE.NAN_JING], this.createParams.gameType) && this.createParams.playType == 0) {
                winall -= 100;
            }
            if (winall > max) {
                max = winall;
            }
        });
        if (this.createParams.fangkaFree <= max) {
            return;
        }

        if (this.extraTimeVoteTimer) {
            clearTimeout(this.extraTimeVoteTimer);
            delete this.extraTimeVoteTimer;
        }
        if (this.tData.extraTimeType == 2 && this.tData.extraNum == 0) { // 开启加时赛投票
            var table = this;
            var voteTime = 60 * 1000;
            table.tData.extraTimeVote.voteStartTime = Date.now();
            table.tData.extraTimeVote.voteEndTime = Date.now() + voteTime;
            table.AllPlayerRun((p) => {
                table.tData.extraTimeVote[p.uid] = {};
                table.tData.extraTimeVote[p.uid].vote = -1;
                if (p.winone >= 0 && p.winall >= 0) {
                    table.tData.extraTimeVote[p.uid].voteDesc = {title: '你赢了！', content:'是否乘胜追击，延长1-2局游戏，扩大积分优势！'};
                } else if (p.winone >= 0 && p.winall < 0) {
                    table.tData.extraTimeVote[p.uid].voteDesc = {title: '你赢了！', content:'分差缩小了！延长1-2局游戏，争取反败为胜！'};
                } else if (p.winone < 0 && p.winall >= 0) {
                    table.tData.extraTimeVote[p.uid].voteDesc = {title: '放手一搏', content:'延长1-2局游戏，拿到更多积分优势！'};
                } else {
                    table.tData.extraTimeVote[p.uid].voteDesc = {title: '放手一搏', content:'放手一搏，是否延长1-2局游戏，缩小积分差距！'};
                }
            });

            this.extraTimeVoteTimer = setTimeout(function() {
                logger.debug('===== 结束投票 ======');
                if (!table.extraTimeVoteTimer) {
                    return;
                }
                clearTimeout(table.extraTimeVoteTimer);
                delete table.extraTimeVoteTimer;
                var playInfo = null;
                if (table.pGameCode && table.pGameCode.EndRoom) {
                    table.tData.roundNum = 0;
                    playInfo = table.pGameCode.EndRoom(table, {});
                }
                table.NotifyAll("voteExtraTime", {extraTimeVote:table.tData.extraTimeVote, playInfo:playInfo});
            }, voteTime);
        }

        this.tData.extraNum++;
        this.tData.roundNum = 1;
    };

    /**
     * 创建快照
     */
    Table.prototype.createSnapshoot = function () {
        return;
    };
    /**
     * 删除快照
     */
    Table.prototype.deleteSnapshoot = function () {
        return;
    };

    // 初始化牌桌积分排行榜
    Table.prototype.initGameTypeScoreRank = function () {
        var clubId = this.createParams.clubId;
        if (!utils.isParamsNotEmpty([clubId, this.createParams.gameType])) {
            return;
        }
        redisSlave.client().zrevrange('RANK_' + clubId + '_' + this.createParams.gameType, 0, -1, 'withscores', (err, results) => {
            if (err) {
                logger.error(err);
                logger.error('获取亲友圈/联盟玩法排行榜失败', clubId, this.createParams.gameType);
                return;
            }
            this.gameTypeScoreRank = {
                list: [], // 排行榜
                userScoreData: {}, // 用户分数数据
                addUserScore: {}, // 本局增加的分数 userId:score
            };
            if (!results || results.length == 0) {
                return;
            }
            for (var i = 0; i < results.length; i++) {
                if (i % 2 == 1 || i + 1 >= results.length) {
                    continue;
                }
                this.gameTypeScoreRank.list.push({ userId: Number(results[i]), score: Number(results[i + 1]) });
                this.gameTypeScoreRank.userScoreData[results[i]] = { userId: Number(results[i]), score: Number(results[i + 1]) };
            };
        });
    };

    /**
     * 更新用户牌局积分排行榜
     * @param playersData object userId:score
     */
    Table.prototype.updateGameTypeScoreRank = function (playersData) {
        var clubId = this.createParams.clubId;
        if (utils.isEmptyObject(playersData) || !utils.isParamsNotEmpty([clubId, this.createParams.gameType])) {
            return;
        }
        logger.debug("用户每局输赢分", JSON.stringify(playersData));
        for (var userId in playersData) {
            playersData[userId] = Number(playersData[userId]);
        }
        for (var userId in playersData) {
            if (this.gameTypeScoreRank.userScoreData[userId]) {
                this.gameTypeScoreRank.userScoreData[userId].score += playersData[userId];
            } else {
                this.gameTypeScoreRank.userScoreData[userId] = { userId: parseInt(userId), score: playersData[userId] };
            }
            if (this.gameTypeScoreRank.addUserScore[userId]) {
                this.gameTypeScoreRank.addUserScore[userId] += playersData[userId];
            } else {
                this.gameTypeScoreRank.addUserScore[userId] = playersData[userId];
            }
        }
        this.gameTypeScoreRank.list = Object.values(this.gameTypeScoreRank.userScoreData);
        this.gameTypeScoreRank.list.sort(function (a, b) {
            if (a.score < b.score) return 1;
            return 0;
        });
        logger.debug("用户积分排行榜", JSON.stringify(this.gameTypeScoreRank));
    };

    Table.prototype.calculateRoundFinalScore = function () {
        this.AllPlayerRun((p) => {
            p.winone2 = p.winone;
        });

        this.AllPlayerRun((p) => {
            p.winone = revise(p.winone);
            p.winone2 = revise(p.winone2);
            p.winall += p.winone;
            p.winall = revise(p.winall);
        });
    };

    Table.prototype.calculateTableFinalScore = function () {
        // var tData = this.tData;
        // if(tData.areaSelectMode.antiAddictionScoreNeedEnough == 1){//防沉迷积分不能为负
        //     var allSubScore = 0;
        //     var allWinScore = 0;            
        //     this.AllPlayerRun((p)=>{
        //         if(p.winall < 0){
        //             if (!p.info.happybean) {
        //                 p.info.happybean = 0;
        //             }
        //             var endScore = p.info.happybean + p.winall;
        //             if(endScore < 0){
        //                 allSubScore += endScore;
        //                 p.winall2 = p.winall - endScore;
        //             }else{
        //                 p.winall2 = p.winall;
        //             }
        //         } else {
        //             allWinScore += p.winall;
        //         }
        //     });

        //     var isReturn = false;
        //     var moreScore = 0;
        //     var pNum = 0
        //     this.AllPlayerRun((p)=>{
        //         if(p.winall > 0){
        //             p.winall2 = p.winall + (p.winall / allWinScore) * allSubScore;
        //             if (!p.info.happybean) {
        //                 p.info.happybean = 0;
        //             }
        //             if (p.winall2 - p.info.happybean > 0) {
        //                 isReturn = true;
        //                 moreScore += p.winall2 - p.info.happybean;
        //                 p.winall2 = p.info.happybean;
        //             }
        //         } else {
        //             pNum++;
        //         }
        //     });

        //     if (isReturn) {
        //         this.AllPlayerRun((p)=>{
        //             if(p.winall < 0){
        //                 p.winall2 = p.winall2+(moreScore/pNum);
        //             } 
        //         });
        //     }


        // }else{
        //     this.AllPlayerRun((p)=> {
        //         p.winall2 = p.winall;
        //     });
        // }
        // this.AllPlayerRun((p)=> {
        //     p.winall = revise(p.winall);
        //     p.winall2 = revise(p.winall2);
        // });
        this.AllPlayerRun((p) => {
            p.winall = revise(p.winall);
            p.winall2 = revise(p.winall);
        });
    };

    Table.prototype.statisticsAnalyse = function () { }
};
